# دليل البدء السريع - Diamond Sales v1.50

## 🚀 التشغيل المباشر (بدون تثبيت)

### الخطوات
1. **اذهب إلى مجلد**: `dist/DiamondSales/`
2. **شغل الملف**: `DiamondSales.exe`
3. **سجل الدخول**: 
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

---

## 📦 إنشاء مثبت

### المتطلبات
- تثبيت Inno Setup من: https://jrsoftware.org/isinfo.php

### الخطوات
1. **شغل الملف**: `build_installer_v1.50.bat`
2. **انتظر**: حتى انتهاء البناء
3. **الملف الناتج**: `installer_output/DiamondSalesSetup_1.50.exe`

---

## ✨ الميزات الجديدة في v1.50

### 1. ملخص الإجماليات المحسن
- تصميم شبكة 2×2 احترافي
- عرض العملتين (دولار وريال)
- يمتد بعرض الجدول بالكامل

### 2. تصدير Excel محسن
- أزرار تصدير Excel جديدة
- تنسيق احترافي مع ألوان مميزة
- ملخص إجماليات في Excel

### 3. إصلاحات مهمة
- ✅ حل خطأ `could not convert string to float`
- ✅ تصحيح رؤوس أعمدة تقرير الموردين
- ✅ تنسيق جداول Excel منتظم

---

## 🔧 استكشاف الأخطاء

### مشكلة: البرنامج لا يفتح
**الحل**: 
- تأكد من Windows 10/11 (64-bit)
- شغل كمدير (Run as Administrator)

### مشكلة: خطأ في قاعدة البيانات
**الحل**:
- احذف ملف `diamond_sales.db`
- أعد تشغيل البرنامج

### مشكلة: تصدير Excel لا يعمل
**الحل**:
- تأكد من وجود Microsoft Excel
- أو استخدم LibreOffice Calc

---

## 📊 اختبار الميزات الجديدة

### تجربة ملخص الإجماليات
1. اذهب إلى **تقارير العملاء**
2. اختر عميل واضغط **عرض التقرير**
3. اضغط **طباعة التقرير**
4. لاحظ التصميم الجديد أسفل الجدول

### تجربة تصدير Excel
1. اذهب إلى **تقارير العملاء** أو **تقارير الموردين**
2. اختر عميل/مورد واضغط **عرض التقرير**
3. اضغط **تصدير Excel** (الزر الجديد)
4. احفظ الملف وافتحه
5. لاحظ التنسيق المحسن وملخص الإجماليات

---

## 📁 هيكل الملفات

```
DiamondSales/
├── DiamondSales.exe          # الملف التنفيذي الرئيسي
├── diamond_sales.db          # قاعدة البيانات
├── assets/                   # الأيقونات والصور
├── translations/             # ملفات الترجمة
└── logs/                     # ملفات السجلات
```

---

## 🆘 الدعم

### ملفات السجلات
- **مجلد السجلات**: `logs/`
- **سجل الأخطاء**: `error_log.txt`

### النسخ الاحتياطية
- **مجلد النسخ**: `backups/`
- **تلقائية**: عند كل تشغيل

### إعادة تعيين كلمة المرور
```bash
# إذا نسيت كلمة مرور admin
python reset_password.py
```

---

## 📈 نصائح للاستخدام الأمثل

### 1. النسخ الاحتياطية
- انسخ مجلد `backups/` بانتظام
- احتفظ بنسخة من `diamond_sales.db`

### 2. الأداء
- أغلق البرامج الأخرى عند تصدير تقارير كبيرة
- استخدم SSD لتحسين الأداء

### 3. الأمان
- غير كلمة مرور admin الافتراضية
- أنشئ مستخدمين بصلاحيات محدودة

---

## 🎯 الخطوات التالية

1. **جرب الميزات الجديدة** في بيئة اختبار
2. **انسخ بياناتك** من الإصدار السابق
3. **درب المستخدمين** على الميزات الجديدة
4. **راقب الأداء** وأرسل ملاحظاتك

---

**إصدار**: 1.50  
**تاريخ البناء**: 2025-06-29  
**حالة البناء**: ✅ نجح  
**الدعم**: فريق تطوير Diamond Sales
