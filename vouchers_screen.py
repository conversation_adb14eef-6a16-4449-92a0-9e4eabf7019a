from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QPushButton, QLabel, QLineEdit, QComboBox,
                            QTableWidget, QTableWidgetItem, QFormLayout,
                            QDateEdit, QDoubleSpinBox, QMessageBox,
                            QHeaderView, QDialog, QDialogButtonBox,
                            QRadioButton, QButtonGroup, QCheckBox)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from sqlalchemy import desc, or_
from datetime import datetime

from database import Receipt, Sale, Purchase, Customer, Supplier, JournalEntry, ChartOfAccounts, صندوق_النقدية, حركة_نقدية, ReceiptItem, Category, Unit
from inventory_manager import update_inventory_for_receipt, validate_inventory_availability
from db_session import session_scope
from logger import log_error, log_info
from ui_utils import style_button, style_dialog_buttons
from translations import get_translation as _

class VouchersScreen(QWidget):
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_vouchers_data()

    def init_ui(self):
        self.setWindowTitle(_("vouchers_title", "نظام إدارة مبيعات الألماس - إدارة السندات"))
        self.setGeometry(100, 100, 1000, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.vouchers_tab = QWidget()
        self.new_receipt_tab = QWidget()
        self.new_payment_tab = QWidget()

        # Setup tabs
        self.setup_vouchers_tab()
        self.setup_new_receipt_tab()
        self.setup_new_payment_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.vouchers_tab, "قائمة السندات")
        self.tab_widget.addTab(self.new_receipt_tab, "سند قبض جديد")
        self.tab_widget.addTab(self.new_payment_tab, "سند صرف جديد")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_vouchers_tab(self):
        # Create layout for vouchers tab
        layout = QVBoxLayout(self.vouchers_tab)

        # Create search controls
        search_layout = QHBoxLayout()

        self.search_customer_supplier = QLineEdit()
        self.search_customer_supplier.setPlaceholderText("بحث باسم العميل أو المورد")

        self.search_date_from = QDateEdit()
        self.search_date_from.setDisplayFormat("dd/MM/yyyy")
        self.search_date_from.setCalendarPopup(True)
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))  # Last 30 days by default

        self.search_date_to = QDateEdit()
        self.search_date_to.setDisplayFormat("dd/MM/yyyy")
        self.search_date_to.setCalendarPopup(True)
        self.search_date_to.setDate(QDate.currentDate())

        self.search_receipt_type = QComboBox()
        self.search_receipt_type.addItem("الكل")
        self.search_receipt_type.addItem("قبض")
        self.search_receipt_type.addItem("صرف")        # تسجيل العناصر المتاحة في القائمة المنسدلة للتشخيص
        log_info("خيارات نوع السند المتاحة:")
        for i in range(self.search_receipt_type.count()):
            log_info(f"  {i}: {self.search_receipt_type.itemText(i)}")
            
        search_button = QPushButton("بحث")
        search_button.clicked.connect(self.search_vouchers)
        style_button(search_button, "info", min_width=120, min_height=35)

        # إضافة زر تحديث القائمة
        refresh_button = QPushButton("تحديث القائمة")
        refresh_button.clicked.connect(self.refresh_vouchers)
        style_button(refresh_button, "success", min_width=150, min_height=35)

        search_layout.addWidget(QLabel("العميل/المورد:"))
        search_layout.addWidget(self.search_customer_supplier)
        search_layout.addWidget(QLabel("من تاريخ:"))
        search_layout.addWidget(self.search_date_from)
        search_layout.addWidget(QLabel("إلى تاريخ:"))
        search_layout.addWidget(self.search_date_to)
        search_layout.addWidget(QLabel("نوع السند:"))
        search_layout.addWidget(self.search_receipt_type)
        search_layout.addWidget(search_button)
        search_layout.addWidget(refresh_button)

        # Create vouchers table
        self.vouchers_table = QTableWidget()
        self.vouchers_table.setColumnCount(7)
        self.vouchers_table.setHorizontalHeaderLabels([
            "رقم السند", "نوع السند", "المرجع", "العميل/المورد",
            "المبلغ ($)", "المبلغ (ريال)", "تاريخ الإصدار"
        ])
        header = self.vouchers_table.horizontalHeader()
        for i in range(7):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)        # Create action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        action_layout.setContentsMargins(5, 5, 5, 15)  # هوامش لمجموعة الأزرار

        view_button = QPushButton("عرض التفاصيل")
        view_button.clicked.connect(self.view_voucher_details)
        style_button(view_button, "info")

        edit_button = QPushButton("تعديل")
        edit_button.clicked.connect(self.edit_voucher)
        style_button(edit_button, "edit")

        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_voucher)
        style_button(delete_button, "delete")

        print_button = QPushButton("طباعة السند")
        print_button.clicked.connect(self.print_voucher)
        style_button(print_button, "default")

        action_layout.addWidget(view_button)
        action_layout.addWidget(edit_button)
        action_layout.addWidget(delete_button)
        action_layout.addWidget(print_button)

        # Add widgets to layout
        layout.addLayout(search_layout)
        layout.addWidget(self.vouchers_table)
        layout.addLayout(action_layout)

    def setup_new_receipt_tab(self):
        # Create layout for new receipt tab
        layout = QVBoxLayout(self.new_receipt_tab)

        # Create form layout for receipt details
        form_layout = QFormLayout()

        # Create title label
        title_label = QLabel("إنشاء سند قبض جديد")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Customer selection
        self.customer_combo = QComboBox()
        self.load_customers()

        # Reference selection (sales for this customer)
        self.sale_combo = QComboBox()
        self.customer_combo.currentIndexChanged.connect(self.load_customer_sales)

        # Currency selection
        currency_layout = QHBoxLayout()
        self.receipt_currency_group = QButtonGroup()

        self.usd_radio = QRadioButton("دولار أمريكي $")
        self.sar_radio = QRadioButton("ريال سعودي ﷼")
        self.usd_radio.setChecked(True)  # Default to USD

        self.receipt_currency_group.addButton(self.usd_radio, 1)
        self.receipt_currency_group.addButton(self.sar_radio, 2)

        currency_layout.addWidget(self.usd_radio)
        currency_layout.addWidget(self.sar_radio)

        currency_widget = QWidget()
        currency_widget.setLayout(currency_layout)
        form_layout.addRow("عملة السداد:", currency_widget)

        # Connect currency radio buttons to update handler
        self.usd_radio.toggled.connect(self.toggle_receipt_currency)
        self.sar_radio.toggled.connect(self.toggle_receipt_currency)

        # Amount input USD
        self.amount_usd_spin = QDoubleSpinBox()
        self.amount_usd_spin.setRange(-1000000.0, 1000000.0)  # السماح بالقيم السالبة
        self.amount_usd_spin.setDecimals(3)
        self.amount_usd_spin.setSingleStep(10.0)
        self.amount_usd_spin.setPrefix("$ ")
        self.amount_usd_spin.valueChanged.connect(self.calculate_receipt_sar)

        # Amount input SAR
        self.amount_sar_spin = QDoubleSpinBox()
        self.amount_sar_spin.setRange(-10000000.0, 10000000.0)  # السماح بالقيم السالبة
        self.amount_sar_spin.setDecimals(3)
        self.amount_sar_spin.setSingleStep(50.0)
        self.amount_sar_spin.setPrefix("﷼ ")
        self.amount_sar_spin.valueChanged.connect(self.calculate_receipt_usd)
        self.amount_sar_spin.setVisible(False)  # Initially hidden

        # SAR amount display
        self.amount_sar_label = QLabel("0.00 ريال")

        # USD amount display (used when input is in SAR)
        self.amount_usd_label = QLabel("0.00 دولار")
        self.amount_usd_label.setVisible(False)  # Initially hidden

        # Exchange rate
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.01, 10.0)
        self.exchange_rate_spin.setDecimals(3)
        self.exchange_rate_spin.setSingleStep(0.01)
        self.exchange_rate_spin.setValue(3.75)  # Default SAR to USD rate
        self.exchange_rate_spin.valueChanged.connect(self.update_receipt_conversion)

        # Date field
        self.receipt_date = QDateEdit()
        self.receipt_date.setDisplayFormat("dd/MM/yyyy")
        self.receipt_date.setCalendarPopup(True)
        self.receipt_date.setDate(QDate.currentDate())

        # Add fields to form layout
        form_layout.addRow("العميل:", self.customer_combo)
        form_layout.addRow("الفاتورة المرجعية:", self.sale_combo)
        form_layout.addRow("المبلغ بالدولار:", self.amount_usd_spin)
        form_layout.addRow("المبلغ بالريال:", self.amount_sar_spin)
        form_layout.addRow("المبلغ بالريال:", self.amount_sar_label)
        form_layout.addRow("المبلغ بالدولار:", self.amount_usd_label)
        form_layout.addRow("سعر الصرف (ريال/دولار):", self.exchange_rate_spin)
        form_layout.addRow("تاريخ السند:", self.receipt_date)

        # إضافة خيار السداد بالأصناف
        self.receipt_with_items_checkbox = QCheckBox("سداد بالأصناف")
        self.receipt_with_items_checkbox.toggled.connect(self.toggle_receipt_items_section)
        form_layout.addRow("نوع السداد:", self.receipt_with_items_checkbox)

        # قسم الأصناف (مخفي افتراضياً)
        self.receipt_items_widget = QWidget()
        self.setup_receipt_items_section()
        self.receipt_items_widget.setVisible(False)

        # Create button to save receipt
        save_button = QPushButton("حفظ سند القبض")
        save_button.clicked.connect(self.save_receipt)
        style_button(save_button, "add", min_width=200, min_height=45)

        # Add widgets to layout
        layout.addLayout(form_layout)
        layout.addWidget(self.receipt_items_widget)
        layout.addWidget(save_button)

        # Hide/show appropriate fields at start
        self.toggle_receipt_currency()

    def setup_receipt_items_section(self):
        """إعداد قسم الأصناف في سند القبض"""
        layout = QVBoxLayout(self.receipt_items_widget)

        # عنوان القسم
        items_title = QLabel("تفاصيل الأصناف")
        items_title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(items_title)

        # جدول الأصناف
        self.receipt_items_table = QTableWidget()
        self.receipt_items_table.setColumnCount(8)
        self.receipt_items_table.setHorizontalHeaderLabels([
            "الصنف", "الوحدة", "نوع الألماس", "الكمية",
            "سعر الوحدة ($)", "سعر الوحدة (ريال)", "الإجمالي ($)", "الإجمالي (ريال)"
        ])

        # تعديل عرض الأعمدة
        header = self.receipt_items_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(8):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.receipt_items_table)

        # أزرار إدارة الأصناف
        items_buttons_layout = QHBoxLayout()

        add_item_btn = QPushButton("إضافة صنف")
        add_item_btn.clicked.connect(self.add_receipt_item)
        style_button(add_item_btn, "add")

        edit_item_btn = QPushButton("تعديل صنف")
        edit_item_btn.clicked.connect(self.edit_receipt_item)
        style_button(edit_item_btn, "edit")

        delete_item_btn = QPushButton("حذف صنف")
        delete_item_btn.clicked.connect(self.delete_receipt_item)
        style_button(delete_item_btn, "delete")

        items_buttons_layout.addWidget(add_item_btn)
        items_buttons_layout.addWidget(edit_item_btn)
        items_buttons_layout.addWidget(delete_item_btn)
        items_buttons_layout.addStretch()

        layout.addLayout(items_buttons_layout)

        # إجمالي الأصناف
        totals_layout = QHBoxLayout()
        self.receipt_items_total_label = QLabel("إجمالي الأصناف: $0.00 (0.00 ريال)")
        self.receipt_items_total_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        totals_layout.addStretch()
        totals_layout.addWidget(self.receipt_items_total_label)
        layout.addLayout(totals_layout)

        # قائمة لحفظ بيانات الأصناف
        self.receipt_items_data = []

    def toggle_receipt_items_section(self):
        """إظهار/إخفاء قسم الأصناف"""
        is_visible = self.receipt_with_items_checkbox.isChecked()
        self.receipt_items_widget.setVisible(is_visible)

        # إذا تم إلغاء تحديد الأصناف، امسح البيانات
        if not is_visible:
            self.receipt_items_data.clear()
            self.receipt_items_table.setRowCount(0)
            self.update_receipt_items_total()

    def add_receipt_item(self):
        """إضافة صنف جديد لسند القبض"""
        dialog = ReceiptItemDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            item_data = dialog.get_item_data()
            self.receipt_items_data.append(item_data)
            self.update_receipt_items_table()
            self.update_receipt_items_total()

    def edit_receipt_item(self):
        """تعديل صنف في سند القبض"""
        current_row = self.receipt_items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار صنف للتعديل")
            return

        item_data = self.receipt_items_data[current_row]
        dialog = ReceiptItemDialog(self, item_data)
        if dialog.exec() == QDialog.Accepted:
            updated_data = dialog.get_item_data()
            self.receipt_items_data[current_row] = updated_data
            self.update_receipt_items_table()
            self.update_receipt_items_total()

    def delete_receipt_item(self):
        """حذف صنف من سند القبض"""
        current_row = self.receipt_items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار صنف للحذف")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا الصنف؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            del self.receipt_items_data[current_row]
            self.update_receipt_items_table()
            self.update_receipt_items_total()

    def update_receipt_items_table(self):
        """تحديث جدول الأصناف"""
        self.receipt_items_table.setRowCount(len(self.receipt_items_data))

        for row, item in enumerate(self.receipt_items_data):
            self.receipt_items_table.setItem(row, 0, QTableWidgetItem(item['category_name']))
            self.receipt_items_table.setItem(row, 1, QTableWidgetItem(item['unit_name']))
            self.receipt_items_table.setItem(row, 2, QTableWidgetItem(item['diamond_type']))
            self.receipt_items_table.setItem(row, 3, QTableWidgetItem(f"{item['quantity']:.3f}"))
            self.receipt_items_table.setItem(row, 4, QTableWidgetItem(f"{item['price_per_unit_usd']:.3f}"))
            self.receipt_items_table.setItem(row, 5, QTableWidgetItem(f"{item['price_per_unit_sar']:.3f}"))
            self.receipt_items_table.setItem(row, 6, QTableWidgetItem(f"{item['total_price_usd']:.3f}"))
            self.receipt_items_table.setItem(row, 7, QTableWidgetItem(f"{item['total_price_sar']:.3f}"))

    def update_receipt_items_total(self):
        """تحديث إجمالي الأصناف"""
        total_usd = sum(item['total_price_usd'] for item in self.receipt_items_data)
        total_sar = sum(item['total_price_sar'] for item in self.receipt_items_data)

        self.receipt_items_total_label.setText(f"إجمالي الأصناف: ${total_usd:.3f} ({total_sar:.3f} ريال)")

    def toggle_receipt_currency(self):
        """تبديل بين إدخال المبلغ بالدولار أو بالريال"""
        is_usd = self.usd_radio.isChecked()

        # When in USD mode
        self.amount_usd_spin.setVisible(is_usd)
        self.amount_sar_label.setVisible(is_usd)

        # When in SAR mode
        self.amount_sar_spin.setVisible(not is_usd)
        self.amount_usd_label.setVisible(not is_usd)

        # Update conversion based on new mode
        self.update_receipt_conversion()

    def update_receipt_conversion(self):
        """تحديث التحويل بين العملات عند تغيير سعر الصرف أو طريقة الإدخال"""
        if self.usd_radio.isChecked():
            self.calculate_receipt_sar()
        else:
            self.calculate_receipt_usd()

    def calculate_receipt_sar(self):
        """حساب المبلغ بالريال عندما يكون الإدخال بالدولار"""
        # Get input values
        amount_usd = self.amount_usd_spin.value()
        exchange_rate = self.exchange_rate_spin.value()

        # Calculate SAR amount
        amount_sar = amount_usd * exchange_rate

        # Update label
        self.amount_sar_label.setText(f"{amount_sar:.3f} ريال")

    def calculate_receipt_usd(self):
        """حساب المبلغ بالدولار عندما يكون الإدخال بالريال"""
        # Get input values
        amount_sar = self.amount_sar_spin.value()
        exchange_rate = self.exchange_rate_spin.value()

        if exchange_rate > 0:
            # Calculate USD amount
            amount_usd = amount_sar / exchange_rate

            # Update label
            self.amount_usd_label.setText(f"{amount_usd:.3f} دولار")

    def setup_new_payment_tab(self):
        # Create layout for new payment tab
        layout = QVBoxLayout(self.new_payment_tab)

        # Create form layout for payment details
        form_layout = QFormLayout()

        # Create title label
        title_label = QLabel("إنشاء سند صرف جديد")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Supplier selection
        self.supplier_combo = QComboBox()
        self.load_suppliers()

        # Reference selection (purchases for this supplier)
        self.purchase_combo = QComboBox()
        self.supplier_combo.currentIndexChanged.connect(self.load_supplier_purchases)

        # Currency selection
        currency_layout = QHBoxLayout()
        self.payment_currency_group = QButtonGroup()

        self.payment_usd_radio = QRadioButton("دولار أمريكي $")
        self.payment_sar_radio = QRadioButton("ريال سعودي ﷼")
        self.payment_usd_radio.setChecked(True)  # Default to USD

        self.payment_currency_group.addButton(self.payment_usd_radio, 1)
        self.payment_currency_group.addButton(self.payment_sar_radio, 2)

        currency_layout.addWidget(self.payment_usd_radio)
        currency_layout.addWidget(self.payment_sar_radio)

        currency_widget = QWidget()
        currency_widget.setLayout(currency_layout)
        form_layout.addRow("عملة السداد:", currency_widget)

        # Connect currency radio buttons to update handler
        self.payment_usd_radio.toggled.connect(self.toggle_payment_currency)
        self.payment_sar_radio.toggled.connect(self.toggle_payment_currency)

        # Amount input USD
        self.payment_amount_usd_spin = QDoubleSpinBox()
        self.payment_amount_usd_spin.setRange(-1000000.0, 1000000.0)  # السماح بالقيم السالبة
        self.payment_amount_usd_spin.setDecimals(3)
        self.payment_amount_usd_spin.setSingleStep(10.0)
        self.payment_amount_usd_spin.setPrefix("$ ")
        self.payment_amount_usd_spin.valueChanged.connect(self.calculate_payment_sar)

        # Amount input SAR
        self.payment_amount_sar_spin = QDoubleSpinBox()
        self.payment_amount_sar_spin.setRange(-10000000.0, 10000000.0)  # السماح بالقيم السالبة
        self.payment_amount_sar_spin.setDecimals(3)
        self.payment_amount_sar_spin.setSingleStep(50.0)
        self.payment_amount_sar_spin.setPrefix("﷼ ")
        self.payment_amount_sar_spin.valueChanged.connect(self.calculate_payment_usd)
        self.payment_amount_sar_spin.setVisible(False)  # Initially hidden

        # SAR amount display
        self.payment_amount_sar_label = QLabel("0.00 ريال")

        # USD amount display (used when input is in SAR)
        self.payment_amount_usd_label = QLabel("0.00 دولار")
        self.payment_amount_usd_label.setVisible(False)  # Initially hidden

        # Exchange rate
        self.payment_exchange_rate_spin = QDoubleSpinBox()
        self.payment_exchange_rate_spin.setRange(0.01, 10.0)
        self.payment_exchange_rate_spin.setDecimals(3)
        self.payment_exchange_rate_spin.setSingleStep(0.01)
        self.payment_exchange_rate_spin.setValue(3.75)  # Default SAR to USD rate
        self.payment_exchange_rate_spin.valueChanged.connect(self.update_payment_conversion)

        # Date field
        self.payment_date = QDateEdit()
        self.payment_date.setDisplayFormat("dd/MM/yyyy")
        self.payment_date.setCalendarPopup(True)
        self.payment_date.setDate(QDate.currentDate())

        # Add fields to form layout
        form_layout.addRow("المورد:", self.supplier_combo)
        form_layout.addRow("الفاتورة المرجعية:", self.purchase_combo)
        form_layout.addRow("المبلغ بالدولار:", self.payment_amount_usd_spin)
        form_layout.addRow("المبلغ بالريال:", self.payment_amount_sar_spin)
        form_layout.addRow("المبلغ بالريال:", self.payment_amount_sar_label)
        form_layout.addRow("المبلغ بالدولار:", self.payment_amount_usd_label)
        form_layout.addRow("سعر الصرف (ريال/دولار):", self.payment_exchange_rate_spin)
        form_layout.addRow("تاريخ السند:", self.payment_date)

        # إضافة خيار الصرف بالأصناف
        self.payment_with_items_checkbox = QCheckBox("صرف بالأصناف")
        self.payment_with_items_checkbox.toggled.connect(self.toggle_payment_items_section)
        form_layout.addRow("نوع الصرف:", self.payment_with_items_checkbox)

        # قسم الأصناف (مخفي افتراضياً)
        self.payment_items_widget = QWidget()
        self.setup_payment_items_section()
        self.payment_items_widget.setVisible(False)

        # Create button to save payment
        save_button = QPushButton("حفظ سند الصرف")
        save_button.clicked.connect(self.save_payment)
        style_button(save_button, "add", min_width=200, min_height=45)

        # Add widgets to layout
        layout.addLayout(form_layout)
        layout.addWidget(self.payment_items_widget)
        layout.addWidget(save_button)

        # Hide/show appropriate fields at start
        self.toggle_payment_currency()

    def setup_payment_items_section(self):
        """إعداد قسم الأصناف في سند الصرف"""
        layout = QVBoxLayout(self.payment_items_widget)

        # عنوان القسم
        items_title = QLabel("تفاصيل الأصناف")
        items_title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(items_title)

        # جدول الأصناف
        self.payment_items_table = QTableWidget()
        self.payment_items_table.setColumnCount(8)
        self.payment_items_table.setHorizontalHeaderLabels([
            "الصنف", "الوحدة", "نوع الألماس", "الكمية",
            "سعر الوحدة ($)", "سعر الوحدة (ريال)", "الإجمالي ($)", "الإجمالي (ريال)"
        ])

        # تعديل عرض الأعمدة
        header = self.payment_items_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(8):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.payment_items_table)

        # أزرار إدارة الأصناف
        items_buttons_layout = QHBoxLayout()

        add_item_btn = QPushButton("إضافة صنف")
        add_item_btn.clicked.connect(self.add_payment_item)
        style_button(add_item_btn, "add")

        edit_item_btn = QPushButton("تعديل صنف")
        edit_item_btn.clicked.connect(self.edit_payment_item)
        style_button(edit_item_btn, "edit")

        delete_item_btn = QPushButton("حذف صنف")
        delete_item_btn.clicked.connect(self.delete_payment_item)
        style_button(delete_item_btn, "delete")

        items_buttons_layout.addWidget(add_item_btn)
        items_buttons_layout.addWidget(edit_item_btn)
        items_buttons_layout.addWidget(delete_item_btn)
        items_buttons_layout.addStretch()

        layout.addLayout(items_buttons_layout)

        # إجمالي الأصناف
        totals_layout = QHBoxLayout()
        self.payment_items_total_label = QLabel("إجمالي الأصناف: $0.00 (0.00 ريال)")
        self.payment_items_total_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        totals_layout.addStretch()
        totals_layout.addWidget(self.payment_items_total_label)
        layout.addLayout(totals_layout)

        # قائمة لحفظ بيانات الأصناف
        self.payment_items_data = []

    def toggle_payment_items_section(self):
        """إظهار/إخفاء قسم الأصناف في سند الصرف"""
        is_visible = self.payment_with_items_checkbox.isChecked()
        self.payment_items_widget.setVisible(is_visible)

        # إذا تم إلغاء تحديد الأصناف، امسح البيانات
        if not is_visible:
            self.payment_items_data.clear()
            self.payment_items_table.setRowCount(0)
            self.update_payment_items_total()

    def add_payment_item(self):
        """إضافة صنف جديد لسند الصرف"""
        dialog = ReceiptItemDialog(self)
        if dialog.exec() == QDialog.Accepted:
            item_data = dialog.get_item_data()
            self.payment_items_data.append(item_data)
            self.update_payment_items_table()
            self.update_payment_items_total()

    def edit_payment_item(self):
        """تعديل صنف في سند الصرف"""
        current_row = self.payment_items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار صنف للتعديل")
            return

        item_data = self.payment_items_data[current_row]
        dialog = ReceiptItemDialog(self, item_data)
        if dialog.exec() == QDialog.Accepted:
            updated_data = dialog.get_item_data()
            self.payment_items_data[current_row] = updated_data
            self.update_payment_items_table()
            self.update_payment_items_total()

    def delete_payment_item(self):
        """حذف صنف من سند الصرف"""
        current_row = self.payment_items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار صنف للحذف")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا الصنف؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            del self.payment_items_data[current_row]
            self.update_payment_items_table()
            self.update_payment_items_total()

    def update_payment_items_table(self):
        """تحديث جدول الأصناف في سند الصرف"""
        self.payment_items_table.setRowCount(len(self.payment_items_data))

        for row, item in enumerate(self.payment_items_data):
            self.payment_items_table.setItem(row, 0, QTableWidgetItem(item['category_name']))
            self.payment_items_table.setItem(row, 1, QTableWidgetItem(item['unit_name']))
            self.payment_items_table.setItem(row, 2, QTableWidgetItem(item['diamond_type']))
            self.payment_items_table.setItem(row, 3, QTableWidgetItem(f"{item['quantity']:.3f}"))
            self.payment_items_table.setItem(row, 4, QTableWidgetItem(f"{item['price_per_unit_usd']:.3f}"))
            self.payment_items_table.setItem(row, 5, QTableWidgetItem(f"{item['price_per_unit_sar']:.3f}"))
            self.payment_items_table.setItem(row, 6, QTableWidgetItem(f"{item['total_price_usd']:.3f}"))
            self.payment_items_table.setItem(row, 7, QTableWidgetItem(f"{item['total_price_sar']:.3f}"))

    def update_payment_items_total(self):
        """تحديث إجمالي الأصناف في سند الصرف"""
        total_usd = sum(item['total_price_usd'] for item in self.payment_items_data)
        total_sar = sum(item['total_price_sar'] for item in self.payment_items_data)

        self.payment_items_total_label.setText(f"إجمالي الأصناف: ${total_usd:.3f} ({total_sar:.3f} ريال)")

    def toggle_payment_currency(self):
        """تبديل بين إدخال المبلغ بالدولار أو بالريال في سند الصرف"""
        is_usd = self.payment_usd_radio.isChecked()

        # When in USD mode
        self.payment_amount_usd_spin.setVisible(is_usd)
        self.payment_amount_sar_label.setVisible(is_usd)

        # When in SAR mode
        self.payment_amount_sar_spin.setVisible(not is_usd)
        self.payment_amount_usd_label.setVisible(not is_usd)

        # Update conversion based on new mode
        self.update_payment_conversion()

    def update_payment_conversion(self):
        """تحديث التحويل بين العملات عند تغيير سعر الصرف أو طريقة الإدخال في سند الصرف"""
        if self.payment_usd_radio.isChecked():
            self.calculate_payment_sar()
        else:
            self.calculate_payment_usd()

    def calculate_payment_usd(self):
        """حساب المبلغ بالدولار عندما يكون الإدخال بالريال في سند الصرف"""
        # Get input values
        amount_sar = self.payment_amount_sar_spin.value()
        exchange_rate = self.payment_exchange_rate_spin.value()

        if exchange_rate > 0:
            # Calculate USD amount
            amount_usd = amount_sar / exchange_rate

            # Update label
            self.payment_amount_usd_label.setText(f"{amount_usd:.3f} دولار")

    def load_customers(self):
        try:
            with session_scope() as session:
                # Get all customers
                customers = session.query(Customer).all()

                # Add customers to combo box
                self.customer_combo.clear()
                self.customer_combo.addItem("-- اختر العميل --", None)
                for customer in customers:
                    self.customer_combo.addItem(customer.name, customer.id)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات العملاء: {str(e)}")

    def load_suppliers(self):
        try:
            with session_scope() as session:
                # Get all suppliers
                suppliers = session.query(Supplier).all()

                # Add suppliers to combo box
                self.supplier_combo.clear()
                self.supplier_combo.addItem("-- اختر المورد --", None)
                for supplier in suppliers:
                    self.supplier_combo.addItem(supplier.name, supplier.id)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الموردين: {str(e)}")

    def load_customer_sales(self):
        # Get selected customer ID
        customer_id = self.customer_combo.currentData()

        if not customer_id:
            self.sale_combo.clear()
            return

        try:
            with session_scope() as session:
                # Get sales for customer with remaining balance
                sales = session.query(Sale).filter(Sale.customer_id == customer_id, Sale.amount_due > 0).all()

                # Update combo box
                self.sale_combo.clear()
                self.sale_combo.addItem("-- حدد فاتورة البيع --", None)
                for sale in sales:
                    date_str = sale.sale_date.strftime("%d/%m/%Y") if sale.sale_date else ""
                    self.sale_combo.addItem(
                        f"فاتورة #{sale.id} - {sale.diamond_type} - {sale.carat_weight} قيراط - ${sale.amount_due:.3f} - {date_str}",
                        sale.id)

                # Add option for non-reference receipt
                self.sale_combo.addItem("سند قبض بدون مرجع", -1)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المبيعات: {str(e)}")

    def load_supplier_purchases(self):
        # Get selected supplier ID
        supplier_id = self.supplier_combo.currentData()

        if not supplier_id:
            self.purchase_combo.clear()
            return

        try:
            with session_scope() as session:
                # Get purchases for supplier with remaining balance
                purchases = session.query(Purchase).filter(Purchase.supplier_id == supplier_id, Purchase.amount_due > 0).all()

                # Update combo box
                self.purchase_combo.clear()
                self.purchase_combo.addItem("-- حدد فاتورة الشراء --", None)
                for purchase in purchases:
                    date_str = purchase.purchase_date.strftime("%d/%m/%Y") if purchase.purchase_date else ""
                    self.purchase_combo.addItem(
                        f"فاتورة #{purchase.id} - {purchase.diamond_type} - {purchase.carat_weight} قيراط - ${purchase.amount_due:.3f} - {date_str}",
                        purchase.id)

                # Add option for non-reference payment
                self.purchase_combo.addItem("سند صرف بدون مرجع", -1)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المشتريات: {str(e)}")

    def calculate_receipt_sar(self):
        # Get input values
        amount_usd = self.amount_usd_spin.value()
        exchange_rate = self.exchange_rate_spin.value()

        # Calculate SAR amount
        amount_sar = amount_usd * exchange_rate

        # Update label
        self.amount_sar_label.setText(f"{amount_sar:.3f} ريال")

    def calculate_payment_sar(self):
        # Get input values
        amount_usd = self.payment_amount_usd_spin.value()
        exchange_rate = self.payment_exchange_rate_spin.value()

        # Calculate SAR amount
        amount_sar = amount_usd * exchange_rate

        # Update label
        self.payment_amount_sar_label.setText(f"{amount_sar:.3f} ريال")

    def save_receipt(self):
        try:
            # Get form data
            customer_id = self.customer_combo.currentData()
            sale_id = self.sale_combo.currentData()
            issue_date = self.receipt_date.date().toPyDate()
            exchange_rate = self.exchange_rate_spin.value()

            # تحديد المبلغ بناءً على العملة المختارة
            is_usd_mode = self.usd_radio.isChecked()

            if is_usd_mode:
                # إذا تم الإدخال بالدولار
                amount_usd = self.amount_usd_spin.value()
                amount_sar = amount_usd * exchange_rate
            else:
                # إذا تم الإدخال بالريال
                amount_sar = self.amount_sar_spin.value()
                amount_usd = amount_sar / exchange_rate if exchange_rate > 0 else 0

            # Validate inputs
            if not customer_id:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار العميل")
                return

            if not sale_id:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار فاتورة البيع أو سند بدون مرجع")
                return

            # التحقق من نوع السداد
            has_items = self.receipt_with_items_checkbox.isChecked()

            if has_items:
                # التحقق من وجود أصناف
                if not self.receipt_items_data:
                    QMessageBox.warning(self, "تحذير", "الرجاء إضافة أصناف للسند")
                    return

                # حساب إجمالي الأصناف
                items_total_usd = sum(item['total_price_usd'] for item in self.receipt_items_data)
                items_total_sar = sum(item['total_price_sar'] for item in self.receipt_items_data)

                # استخدام إجمالي الأصناف كمبلغ السند
                amount_usd = items_total_usd
                amount_sar = items_total_sar
            else:
                # التحقق من المبلغ النقدي
                if amount_usd == 0:
                    QMessageBox.warning(self, "تحذير", "الرجاء إدخال مبلغ صالح (يمكن أن يكون موجباً أو سالباً)")
                    return

            with session_scope() as session:
                try:
                    # Check if amount is greater than balance due for the sale
                    if sale_id > 0:
                        sale = session.query(Sale).filter_by(id=sale_id).first()
                        if not sale:
                            QMessageBox.warning(self, "تحذير", "فاتورة البيع غير موجودة")
                            return

                        if amount_usd > sale.amount_due:
                            QMessageBox.warning(self, "تحذير", f"المبلغ المدخل أكبر من المبلغ المستحق ({sale.amount_due:.3f}$)")
                            return

                    # Create new receipt
                    receipt = Receipt(
                        sale_id=None if sale_id == -1 else sale_id,
                        purchase_id=None,
                        customer_id=customer_id,  # تخزين معرف العميل دائماً
                        supplier_id=None,
                        receipt_type="CashIn",
                        amount_usd=amount_usd,
                        amount_sar=amount_sar,
                        issue_date=issue_date,
                        has_items=has_items,
                        notes="سند قبض بالأصناف" if has_items else None
                    )

                    # Add receipt to database
                    session.add(receipt)
                    session.flush()  # To get the receipt ID

                    # حفظ تفاصيل الأصناف إذا كان السند بالأصناف
                    if has_items:
                        for item_data in self.receipt_items_data:
                            receipt_item = ReceiptItem(
                                receipt_id=receipt.id,
                                category_id=item_data['category_id'],
                                unit_id=item_data['unit_id'],
                                diamond_type=item_data['diamond_type'],
                                quantity=item_data['quantity'],
                                price_per_unit_usd=item_data['price_per_unit_usd'],
                                price_per_unit_sar=item_data['price_per_unit_sar'],
                                total_price_usd=item_data['total_price_usd'],
                                total_price_sar=item_data['total_price_sar'],
                                exchange_rate=item_data['exchange_rate']
                            )
                            session.add(receipt_item)

                    # Update sale if referenced
                    if sale_id > 0:
                        sale.amount_paid += amount_usd
                        sale.amount_due -= amount_usd                    # Create journal entries (debit Cash, credit Customer)
                    cash_account = session.query(ChartOfAccounts).filter_by(name="Cash").first()
                    customer_account = session.query(ChartOfAccounts).filter_by(name="Customers").first()

                    if cash_account and customer_account:
                        # التعامل مع المبالغ السالبة في القيود المحاسبية
                        if amount_usd >= 0:
                            # مبلغ موجب: مدين النقدية، دائن العملاء
                            cash_entry = JournalEntry(
                                account_id=cash_account.id,
                                debit=amount_usd,
                                credit=0,
                                date=issue_date,
                                description=f"سند قبض من العميل - {self.customer_combo.currentText()}",
                                sale_id=None if sale_id == -1 else sale_id,
                                receipt_id=receipt.id
                            )
                            session.add(cash_entry)

                            customer_entry = JournalEntry(
                                account_id=customer_account.id,
                                debit=0,
                                credit=amount_usd,
                                date=issue_date,
                                description=f"سند قبض من العميل - {self.customer_combo.currentText()}",
                                sale_id=None if sale_id == -1 else sale_id,
                                receipt_id=receipt.id
                            )
                            session.add(customer_entry)
                        else:
                            # مبلغ سالب: دائن النقدية، مدين العملاء
                            amount_abs = abs(amount_usd)
                            cash_entry = JournalEntry(
                                account_id=cash_account.id,
                                debit=0,
                                credit=amount_abs,
                                date=issue_date,
                                description=f"سند قبض من العميل (مبلغ سالب) - {self.customer_combo.currentText()}",
                                sale_id=None if sale_id == -1 else sale_id,
                                receipt_id=receipt.id
                            )
                            session.add(cash_entry)

                            customer_entry = JournalEntry(
                                account_id=customer_account.id,
                                debit=amount_abs,
                                credit=0,
                                date=issue_date,
                                description=f"سند قبض من العميل (مبلغ سالب) - {self.customer_combo.currentText()}",
                                sale_id=None if sale_id == -1 else sale_id,
                                receipt_id=receipt.id
                            )
                            session.add(customer_entry)
                          # تحديث صندوق النقدية
                        cash_box = session.query(صندوق_النقدية).first()
                        if not cash_box:
                            cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                            session.add(cash_box)
                            session.flush()
                            
                        # تحديث رصيد الصندوق
                        new_balance = cash_box.balance + amount_usd
                        cash_box.balance = new_balance
                        cash_box.last_updated = datetime.now()
                        
                        # تحديد نوع الحركة بناءً على إشارة المبلغ
                        transaction_type = "deposit" if amount_usd >= 0 else "withdraw"
                        amount_abs = abs(amount_usd)  # استخدام القيمة المطلقة للمبلغ في الحركة
                        
                        # إضافة حركة نقدية
                        transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type=transaction_type,
                            amount=amount_abs,
                            balance_after=new_balance,
                            transaction_date=issue_date,
                            reference=f"سند قبض رقم {receipt.id}",
                            description=f"سند قبض من العميل - {self.customer_combo.currentText()} {'(مبلغ سالب)' if amount_usd < 0 else ''}",
                            created_by=self.user.id if hasattr(self, 'user') else None
                        )
                        session.add(transaction)
                        session.flush()  # ضمان وجود معرّف للحركة النقدية

                    # Commit changes
                    session.commit()

                    # تحديث المخزون إذا كان السند يحتوي على أصناف
                    if has_items:
                        update_inventory_for_receipt(receipt.id, operation="add")

                    # تسجيل العملية
                    log_info(f"تم إنشاء سند قبض جديد رقم {receipt.id} بقيمة {amount_usd:.3f}$ ({amount_sar:.3f} ريال) للعميل {self.customer_combo.currentText()}")

                    # Show success message
                    success_msg = "تم إصدار سند القبض بالأصناف بنجاح" if has_items else "تم إصدار سند القبض بنجاح"
                    QMessageBox.information(self, "نجاح", success_msg)

                    # Reset form
                    self.customer_combo.setCurrentIndex(0)
                    self.amount_usd_spin.setValue(0)
                    self.amount_sar_spin.setValue(0)
                    self.receipt_date.setDate(QDate.currentDate())
                    self.usd_radio.setChecked(True)  # إعادة ضبط العملة إلى الدولار

                    # إعادة تعيين قسم الأصناف
                    self.receipt_with_items_checkbox.setChecked(False)
                    self.receipt_items_data.clear()
                    self.receipt_items_table.setRowCount(0)
                    self.update_receipt_items_total()

                    # Reload vouchers data
                    self.load_vouchers_data()

                    # Switch to vouchers tab
                    self.tab_widget.setCurrentIndex(0)

                except Exception as e:
                    session.rollback()
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ سند القبض: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def save_payment(self):
        try:
            # Get form data
            supplier_id = self.supplier_combo.currentData()
            purchase_id = self.purchase_combo.currentData()
            issue_date = self.payment_date.date().toPyDate()
            exchange_rate = self.payment_exchange_rate_spin.value()

            # تحديد المبلغ بناءً على العملة المختارة
            is_usd_mode = self.payment_usd_radio.isChecked()

            if is_usd_mode:
                # إذا تم الإدخال بالدولار
                amount_usd = self.payment_amount_usd_spin.value()
                amount_sar = amount_usd * exchange_rate
            else:
                # إذا تم الإدخال بالريال
                amount_sar = self.payment_amount_sar_spin.value()
                amount_usd = amount_sar / exchange_rate if exchange_rate > 0 else 0

            # Validate inputs
            if not supplier_id:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار المورد")
                return

            if not purchase_id:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار فاتورة الشراء أو سند بدون مرجع")
                return

            # التحقق من نوع الصرف
            has_items = self.payment_with_items_checkbox.isChecked()

            if has_items:
                # التحقق من وجود أصناف
                if not self.payment_items_data:
                    QMessageBox.warning(self, "تحذير", "الرجاء إضافة أصناف للسند")
                    return

                # التحقق من توفر الأصناف في المخزون (لسندات الصرف)
                for item_data in self.payment_items_data:
                    is_available, available_qty = validate_inventory_availability(
                        diamond_type=item_data['diamond_type'],
                        category_id=item_data['category_id'],
                        unit_id=item_data['unit_id'],
                        required_quantity=item_data['quantity']
                    )

                    if not is_available:
                        QMessageBox.warning(
                            self, "تحذير",
                            f"الكمية المطلوبة من {item_data['diamond_type']} ({item_data['quantity']:.3f}) "
                            f"غير متوفرة في المخزون. الكمية المتاحة: {available_qty:.3f}"
                        )
                        return

                # حساب إجمالي الأصناف
                items_total_usd = sum(item['total_price_usd'] for item in self.payment_items_data)
                items_total_sar = sum(item['total_price_sar'] for item in self.payment_items_data)

                # استخدام إجمالي الأصناف كمبلغ السند
                amount_usd = items_total_usd
                amount_sar = items_total_sar
            else:
                # التحقق من المبلغ النقدي
                if amount_usd == 0:
                    QMessageBox.warning(self, "تحذير", "الرجاء إدخال مبلغ صالح (يمكن أن يكون موجباً أو سالباً)")
                    return

            with session_scope() as session:
                try:
                    # Check if amount is greater than balance due for the purchase
                    if purchase_id > 0:
                        purchase = session.query(Purchase).filter_by(id=purchase_id).first()
                        if not purchase:
                            QMessageBox.warning(self, "تحذير", "فاتورة الشراء غير موجودة")
                            return

                        if amount_usd > purchase.amount_due:
                            QMessageBox.warning(self, "تحذير", f"المبلغ المدخل أكبر من المبلغ المستحق ({purchase.amount_due:.2f}$)")
                            return

                    # Create new receipt
                    receipt = Receipt(
                        sale_id=None,
                        purchase_id=None if purchase_id == -1 else purchase_id,
                        customer_id=None,
                        supplier_id=supplier_id,  # تخزين معرف المورد دائماً
                        receipt_type="CashOut",
                        amount_usd=amount_usd,
                        amount_sar=amount_sar,
                        issue_date=issue_date,
                        has_items=has_items,
                        notes="سند صرف بالأصناف" if has_items else None
                    )

                    # Add receipt to database
                    session.add(receipt)
                    session.flush()  # To get the receipt ID

                    # حفظ تفاصيل الأصناف إذا كان السند بالأصناف
                    if has_items:
                        for item_data in self.payment_items_data:
                            receipt_item = ReceiptItem(
                                receipt_id=receipt.id,
                                category_id=item_data['category_id'],
                                unit_id=item_data['unit_id'],
                                diamond_type=item_data['diamond_type'],
                                quantity=item_data['quantity'],
                                price_per_unit_usd=item_data['price_per_unit_usd'],
                                price_per_unit_sar=item_data['price_per_unit_sar'],
                                total_price_usd=item_data['total_price_usd'],
                                total_price_sar=item_data['total_price_sar'],
                                exchange_rate=item_data['exchange_rate']
                            )
                            session.add(receipt_item)

                    # Update purchase if referenced
                    if purchase_id > 0:
                        purchase.amount_paid += amount_usd
                        purchase.amount_due -= amount_usd                    # Create journal entries (debit Supplier, credit Cash)
                    cash_account = session.query(ChartOfAccounts).filter_by(name="Cash").first()
                    supplier_account = session.query(ChartOfAccounts).filter_by(name="Suppliers").first()

                    if cash_account and supplier_account:
                        # التعامل مع المبالغ السالبة في القيود المحاسبية
                        if amount_usd >= 0:
                            # مبلغ موجب: مدين الموردين، دائن النقدية
                            supplier_entry = JournalEntry(
                                account_id=supplier_account.id,
                                debit=amount_usd,
                                credit=0,
                                date=issue_date,
                                description=f"سند صرف للمورد - {self.supplier_combo.currentText()}",
                                purchase_id=None if purchase_id == -1 else purchase_id,
                                receipt_id=receipt.id
                            )
                            session.add(supplier_entry)

                            cash_entry = JournalEntry(
                                account_id=cash_account.id,
                                debit=0,
                                credit=amount_usd,
                                date=issue_date,
                                description=f"سند صرف للمورد - {self.supplier_combo.currentText()}",
                                purchase_id=None if purchase_id == -1 else purchase_id,
                                receipt_id=receipt.id
                            )
                            session.add(cash_entry)
                        else:
                            # مبلغ سالب: دائن الموردين، مدين النقدية
                            amount_abs = abs(amount_usd)
                            supplier_entry = JournalEntry(
                                account_id=supplier_account.id,
                                debit=0,
                                credit=amount_abs,
                                date=issue_date,
                                description=f"سند صرف للمورد (مبلغ سالب) - {self.supplier_combo.currentText()}",
                                purchase_id=None if purchase_id == -1 else purchase_id,
                                receipt_id=receipt.id
                            )
                            session.add(supplier_entry)

                            cash_entry = JournalEntry(
                                account_id=cash_account.id,
                                debit=amount_abs,
                                credit=0,
                                date=issue_date,
                                description=f"سند صرف للمورد (مبلغ سالب) - {self.supplier_combo.currentText()}",
                                purchase_id=None if purchase_id == -1 else purchase_id,
                                receipt_id=receipt.id
                            )
                            session.add(cash_entry)
                          # تحديث صندوق النقدية
                        cash_box = session.query(صندوق_النقدية).first()
                        if not cash_box:
                            cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                            session.add(cash_box)
                            session.flush()
                            
                        # تحديث رصيد الصندوق
                        new_balance = cash_box.balance - amount_usd  # طرح المبلغ (موجب أو سالب)
                        cash_box.balance = new_balance
                        cash_box.last_updated = datetime.now()
                        
                        # تحديد نوع الحركة بناءً على إشارة المبلغ
                        transaction_type = "withdraw" if amount_usd >= 0 else "deposit"
                        amount_abs = abs(amount_usd)  # استخدام القيمة المطلقة للمبلغ في الحركة
                        
                        # إضافة حركة نقدية
                        transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type=transaction_type,
                            amount=amount_abs,
                            balance_after=new_balance,
                            transaction_date=issue_date,
                            reference=f"سند صرف رقم {receipt.id}",
                            description=f"سند صرف للمورد - {self.supplier_combo.currentText()} {'(مبلغ سالب)' if amount_usd < 0 else ''}",
                            created_by=self.user.id if hasattr(self, 'user') else None
                        )
                        session.add(transaction)
                        session.flush()  # ضمان وجود معرّف للحركة النقدية

                    # Commit changes
                    session.commit()

                    # تحديث المخزون إذا كان السند يحتوي على أصناف
                    if has_items:
                        update_inventory_for_receipt(receipt.id, operation="add")

                    # تسجيل العملية
                    log_info(f"تم إنشاء سند صرف جديد رقم {receipt.id} بقيمة {amount_usd:.3f}$ ({amount_sar:.3f} ريال) للمورد {self.supplier_combo.currentText()}")

                    # Show success message
                    success_msg = "تم إصدار سند الصرف بالأصناف بنجاح" if has_items else "تم إصدار سند الصرف بنجاح"
                    QMessageBox.information(self, "نجاح", success_msg)

                    # Reset form
                    self.supplier_combo.setCurrentIndex(0)
                    self.payment_amount_usd_spin.setValue(0)
                    self.payment_amount_sar_spin.setValue(0)
                    self.payment_date.setDate(QDate.currentDate())
                    self.payment_usd_radio.setChecked(True)  # إعادة ضبط العملة إلى الدولار

                    # إعادة تعيين قسم الأصناف
                    self.payment_with_items_checkbox.setChecked(False)
                    self.payment_items_data.clear()
                    self.payment_items_table.setRowCount(0)
                    self.update_payment_items_total()

                    # Reload vouchers data
                    self.load_vouchers_data()

                    # Switch to vouchers tab
                    self.tab_widget.setCurrentIndex(0)

                except Exception as e:
                    session.rollback()
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ سند الصرف: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def load_vouchers_data(self):
        try:
            # تسجيل بدء عملية تحميل بيانات السندات
            log_info("بدء تحميل بيانات السندات")

            with session_scope() as session:
                # استعلام مباشر لجميع السندات بدون فلترة
                all_receipts = session.query(Receipt).all()
                log_info(f"عدد السندات المسترجعة من قاعدة البيانات: {len(all_receipts)}")

                # طباعة بيانات السندات للتشخيص
                for receipt in all_receipts:
                    log_info(f"معرف السند: {receipt.id}, نوع السند: {receipt.receipt_type}, المبلغ: {receipt.amount_usd}, التاريخ: {receipt.issue_date}")

                result = []

                # معالجة جميع السندات
                for receipt in all_receipts:
                    name = "غير محدد"
                    reference = "بدون مرجع"

                    # تحديد نوع السند بناءً على receipt_type
                    receipt_type = "قبض" if receipt.receipt_type == "CashIn" else "صرف"

                    # استعلام عن معلومات المرجع (العميل أو المورد)
                    if receipt.receipt_type == "CashIn":
                        if receipt.sale_id:
                            # سند قبض مرتبط بمبيعات
                            sale_info = session.query(Sale, Customer).join(Customer).filter(Sale.id == receipt.sale_id).first()
                            if sale_info:
                                sale, customer = sale_info
                                name = customer.name
                                reference = f"مبيعات #{sale.id}"
                        elif receipt.customer_id:
                            # سند قبض بدون مرجع ولكن مرتبط بعميل
                            customer = session.query(Customer).filter_by(id=receipt.customer_id).first()
                            if customer:
                                name = customer.name

                    elif receipt.receipt_type == "CashOut":
                        if receipt.purchase_id:
                            # سند صرف مرتبط بمشتريات
                            purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(Purchase.id == receipt.purchase_id).first()
                            if purchase_info:
                                purchase, supplier = purchase_info
                                name = supplier.name
                                reference = f"مشتريات #{purchase.id}"
                        elif receipt.supplier_id:
                            # سند صرف بدون مرجع ولكن مرتبط بمورد
                            supplier = session.query(Supplier).filter_by(id=receipt.supplier_id).first()
                            if supplier:
                                name = supplier.name

                    # إضافة السند إلى النتائج
                    result.append({
                        "id": receipt.id,
                        "type": receipt_type,
                        "reference": reference,
                        "name": name,
                        "amount_usd": receipt.amount_usd,
                        "amount_sar": receipt.amount_sar,
                        "date": receipt.issue_date
                    })

                log_info(f"عدد السندات بعد المعالجة: {len(result)}")

                # فرز النتائج حسب التاريخ (الأحدث أولاً)
                result.sort(key=lambda x: x["date"] if x["date"] else datetime.min, reverse=True)

                # تحديث جدول السندات
                self.vouchers_table.clearContents()
                self.vouchers_table.setRowCount(len(result))

                for i, data in enumerate(result):
                    self.vouchers_table.setItem(i, 0, QTableWidgetItem(str(data["id"])))
                    self.vouchers_table.setItem(i, 1, QTableWidgetItem(data["type"]))
                    self.vouchers_table.setItem(i, 2, QTableWidgetItem(data["reference"]))
                    self.vouchers_table.setItem(i, 3, QTableWidgetItem(data["name"]))
                    self.vouchers_table.setItem(i, 4, QTableWidgetItem(f"{data['amount_usd']:.3f}"))
                    if data['amount_sar'] is not None:
                        self.vouchers_table.setItem(i, 5, QTableWidgetItem(f"{data['amount_sar']:.3f}"))
                    else:
                        self.vouchers_table.setItem(i, 5, QTableWidgetItem(""))

                    # تنسيق التاريخ
                    date_str = data["date"].strftime("%d/%m/%Y") if data["date"] else ""
                    self.vouchers_table.setItem(i, 6, QTableWidgetItem(date_str))

                # تسجيل اكتمال عملية التحميل
                log_info(f"تم تحميل {len(result)} سند في الجدول")

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات السندات: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات السندات: {str(e)}")

    def refresh_vouchers(self):
        """تحديث قائمة السندات بدون تطبيق أي فلاتر"""
        # إعادة تعيين معايير البحث
        self.search_customer_supplier.clear()
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))
        self.search_date_to.setDate(QDate.currentDate())
        self.search_receipt_type.setCurrentIndex(0)  # اختيار "الكل"

        # تحميل البيانات
        self.load_vouchers_data()

        # تسجيل العملية
        log_info("تم تحديث قائمة السندات")

    def search_vouchers(self):
        """البحث عن السندات حسب المعايير المحددة"""
        # الحصول على معايير البحث
        search_text = self.search_customer_supplier.text().strip()
        date_from = self.search_date_from.date().toPyDate()
        date_to = self.search_date_to.date().toPyDate()
        receipt_type_text = self.search_receipt_type.currentText()

        # تحويل نص نوع السند إلى قيمة في قاعدة البيانات
        receipt_type_db = None
        if receipt_type_text == "قبض":
            receipt_type_db = "CashIn"
        elif receipt_type_text == "صرف":
            receipt_type_db = "CashOut"

        # تسجيل معايير البحث للتشخيص
        log_info(f"معايير البحث: نص='{search_text}', من={date_from}, إلى={date_to}, نوع السند='{receipt_type_text}' (DB: {receipt_type_db})")
        log_info(f"مؤشر نوع السند: {self.search_receipt_type.currentIndex()}")

        try:
            with session_scope() as session:
                # استعلام قاعدة البيانات
                receipts = []
                result = []

                # بناء الاستعلام الأساسي
                base_query = session.query(Receipt).filter(
                    Receipt.issue_date.between(date_from, date_to)
                )

                # إضافة فلتر نوع السند إذا تم تحديده
                if receipt_type_db:
                    log_info(f"تطبيق فلتر نوع السند: {receipt_type_db}")
                    receipts = base_query.filter(Receipt.receipt_type == receipt_type_db).all()
                else:
                    log_info("استرجاع جميع أنواع السندات")
                    receipts = base_query.all()

                # تسجيل عدد السندات المسترجعة
                log_info(f"تم استرجاع {len(receipts)} سند من قاعدة البيانات")

                # معالجة كل سند
                for receipt in receipts:
                    # تسجيل معلومات السند للتشخيص
                    log_info(f"معالجة السند: ID={receipt.id}, النوع={receipt.receipt_type}, التاريخ={receipt.issue_date}")

                    name = "غير محدد"
                    reference = "بدون مرجع"

                    # معالجة سندات القبض
                    if receipt.receipt_type == "CashIn":
                        # إذا كان السند مرتبط بعملية بيع
                        if receipt.sale_id:
                            sale_info = session.query(Sale, Customer).join(Customer).filter(
                                Sale.id == receipt.sale_id
                            ).first()

                            if sale_info:
                                sale, customer = sale_info
                                name = customer.name
                                reference = f"مبيعات #{sale.id}"

                                # فلترة بالنص إذا تم إدخال نص بحث
                                if search_text and search_text not in customer.name:
                                    log_info(f"تخطي السند {receipt.id} - لا يتطابق نص البحث مع اسم العميل")
                                    continue

                        # إذا كان السند مرتبط بعميل بدون مرجع
                        elif receipt.customer_id:
                            customer = session.query(Customer).filter(
                                Customer.id == receipt.customer_id
                            ).first()

                            if customer:
                                name = customer.name

                                # فلترة بالنص إذا تم إدخال نص بحث
                                if search_text and search_text not in customer.name:
                                    log_info(f"تخطي السند {receipt.id} - لا يتطابق نص البحث مع اسم العميل")
                                    continue

                        # تخطي السندات بدون مرجع وبدون عميل إذا كان هناك نص بحث
                        elif search_text:
                            log_info(f"تخطي السند {receipt.id} - بدون مرجع وبدون عميل")
                            continue

                        # إضافة السند إلى النتائج
                        result.append({
                            "id": receipt.id,
                            "type": "قبض",
                            "reference": reference,
                            "name": name,
                            "amount_usd": receipt.amount_usd,
                            "amount_sar": receipt.amount_sar,
                            "date": receipt.issue_date
                        })

                    # معالجة سندات الصرف
                    elif receipt.receipt_type == "CashOut":
                        # إذا كان السند مرتبط بعملية شراء
                        if receipt.purchase_id:
                            purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(
                                Purchase.id == receipt.purchase_id
                            ).first()

                            if purchase_info:
                                purchase, supplier = purchase_info
                                name = supplier.name
                                reference = f"مشتريات #{purchase.id}"

                                # فلترة بالنص إذا تم إدخال نص بحث
                                if search_text and search_text not in supplier.name:
                                    log_info(f"تخطي السند {receipt.id} - لا يتطابق نص البحث مع اسم المورد")
                                    continue

                        # إذا كان السند مرتبط بمورد بدون مرجع
                        elif receipt.supplier_id:
                            supplier = session.query(Supplier).filter(
                                Supplier.id == receipt.supplier_id
                            ).first()

                            if supplier:
                                name = supplier.name

                                # فلترة بالنص إذا تم إدخال نص بحث
                                if search_text and search_text not in supplier.name:
                                    log_info(f"تخطي السند {receipt.id} - لا يتطابق نص البحث مع اسم المورد")
                                    continue

                        # تخطي السندات بدون مرجع وبدون مورد إذا كان هناك نص بحث
                        elif search_text:
                            log_info(f"تخطي السند {receipt.id} - بدون مرجع وبدون مورد")
                            continue

                        # إضافة السند إلى النتائج
                        result.append({
                            "id": receipt.id,
                            "type": "صرف",
                            "reference": reference,
                            "name": name,
                            "amount_usd": receipt.amount_usd,
                            "amount_sar": receipt.amount_sar,
                            "date": receipt.issue_date
                        })

                # تسجيل عدد النتائج النهائية
                log_info(f"عدد النتائج النهائية بعد الفلترة: {len(result)}")

                # ترتيب النتائج حسب التاريخ (الأحدث أولاً)
                result.sort(key=lambda x: x["date"] if x["date"] else datetime.min, reverse=True)

                # تحديث الجدول
                self.vouchers_table.clearContents()
                self.vouchers_table.setRowCount(len(result))

                for i, data in enumerate(result):
                    self.vouchers_table.setItem(i, 0, QTableWidgetItem(str(data["id"])))
                    self.vouchers_table.setItem(i, 1, QTableWidgetItem(data["type"]))
                    self.vouchers_table.setItem(i, 2, QTableWidgetItem(data["reference"]))
                    self.vouchers_table.setItem(i, 3, QTableWidgetItem(data["name"]))
                    self.vouchers_table.setItem(i, 4, QTableWidgetItem(f"{data['amount_usd']:.3f}"))
                    if data['amount_sar'] is not None:
                        self.vouchers_table.setItem(i, 5, QTableWidgetItem(f"{data['amount_sar']:.3f}"))
                    else:
                        self.vouchers_table.setItem(i, 5, QTableWidgetItem(""))

                    # تنسيق التاريخ
                    date_str = data["date"].strftime("%d/%m/%Y") if data["date"] else ""
                    self.vouchers_table.setItem(i, 6, QTableWidgetItem(date_str))

                    # تسجيل بيانات الصف المضاف للتشخيص
                    log_info(f"إضافة صف {i}: ID={data['id']}, النوع={data['type']}, المرجع={data['reference']}")

                # إظهار رسالة إذا لم يتم العثور على نتائج
                if len(result) == 0:
                    log_info("لم يتم العثور على أي سندات تطابق معايير البحث")
                    QMessageBox.information(self, "نتائج البحث", "لم يتم العثور على أي سندات تطابق معايير البحث")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def view_voucher_details(self):
        # Get selected row
        selected_row = self.vouchers_table.currentRow()

        if (selected_row < 0):
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار سند لعرضه")
            return

        # Get voucher ID
        receipt_id = int(self.vouchers_table.item(selected_row, 0).text())

        # Show details in a dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("تفاصيل السند")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        try:
            with session_scope() as session:
                # Get receipt
                receipt = session.query(Receipt).filter_by(id=receipt_id).first()

                if not receipt:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على السند")
                    return

                # Get related information
                receipt_type = "قبض" if receipt.receipt_type == "CashIn" else "صرف"

                # Create labels with receipt information
                info_text = f"""
                <h3>تفاصيل السند رقم {receipt.id}</h3>
                <p><b>نوع السند:</b> {receipt_type}</p>
                <p><b>المبلغ بالدولار:</b> {receipt.amount_usd:.2f} $</p>
                <p><b>المبلغ بالريال:</b> {receipt.amount_sar:.2f} ريال</p>
                <p><b>تاريخ الإصدار:</b> {receipt.issue_date.strftime("%d/%m/%Y") if receipt.issue_date else ""}</p>
                """

                # Get related sale or purchase information
                if receipt.sale_id:
                    sale = session.query(Sale).filter_by(id=receipt.sale_id).first()
                    if sale:
                        customer = session.query(Customer).filter_by(id=sale.customer_id).first()
                        customer_name = customer.name if customer else "غير محدد"
                        info_text += f"""
                        <p><b>مرجع:</b> فاتورة البيع رقم {sale.id}</p>
                        <p><b>العميل:</b> {customer_name}</p>
                        <p><b>نوع الألماس:</b> {sale.diamond_type}</p>
                        <p><b>الوزن بالقيراط:</b> {sale.carat_weight:.2f}</p>
                        """

                elif receipt.purchase_id:
                    purchase = session.query(Purchase).filter_by(id=receipt.purchase_id).first()
                    if purchase:
                        supplier = session.query(Supplier).filter_by(id=purchase.supplier_id).first()
                        supplier_name = supplier.name if supplier else "غير محدد"
                        info_text += f"""
                        <p><b>مرجع:</b> فاتورة الشراء رقم {purchase.id}</p>
                        <p><b>المورد:</b> {supplier_name}</p>
                        <p><b>نوع الألماس:</b> {purchase.diamond_type}</p>
                        <p><b>الوزن بالقيراط:</b> {purchase.carat_weight:.2f}</p>
                        """
                elif receipt.customer_id:
                    # سند قبض بدون مرجع ولكن مرتبط بعميل
                    customer = session.query(Customer).filter_by(id=receipt.customer_id).first()
                    if customer:
                        info_text += f"""
                        <p><b>مرجع:</b> بدون مرجع</p>
                        <p><b>العميل:</b> {customer.name}</p>
                        <p><b>رقم الهوية:</b> {customer.id_number or 'غير محدد'}</p>
                        <p><b>رقم الهاتف:</b> {customer.phone or 'غير محدد'}</p>
                        """
                elif receipt.supplier_id:
                    # سند صرف بدون مرجع ولكن مرتبط بمورد
                    supplier = session.query(Supplier).filter_by(id=receipt.supplier_id).first()
                    if supplier:
                        info_text += f"""
                        <p><b>مرجع:</b> بدون مرجع</p>
                        <p><b>المورد:</b> {supplier.name}</p>
                        <p><b>رقم الهوية:</b> {supplier.id_number or 'غير محدد'}</p>
                        <p><b>رقم الهاتف:</b> {supplier.phone or 'غير محدد'}</p>
                        """
                else:
                    info_text += "<p><b>مرجع:</b> بدون مرجع</p>"

                info_label = QLabel(info_text)
                info_label.setTextFormat(Qt.RichText)
                layout.addWidget(info_label)                # Add a close button
                buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
                buttons.rejected.connect(dialog.reject)
                style_dialog_buttons(buttons)
                layout.addWidget(buttons)

                # Show the dialog
                dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض تفاصيل السند: {str(e)}")

    def delete_voucher(self):
        # Get selected row
        selected_row = self.vouchers_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار سند لحذفه")
            return

        # Get receipt ID and type
        receipt_id = int(self.vouchers_table.item(selected_row, 0).text())
        receipt_type = self.vouchers_table.item(selected_row, 1).text()

        # Confirm deletion
        reply = QMessageBox.question(self, "تأكيد",
                                    f"هل أنت متأكد من رغبتك في حذف سند ال{receipt_type} هذا؟\n"
                                    "سيتم حذف جميع القيود المحاسبية المرتبطة به أيضاً.",
                                    QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                with session_scope() as session:
                    # Get receipt
                    receipt = session.query(Receipt).filter_by(id=receipt_id).first()

                    if not receipt:
                        QMessageBox.warning(self, "تحذير", "لم يتم العثور على السند")
                        return

                    # If receipt is linked to sale or purchase, update the balance
                    if receipt.sale_id:
                        sale = session.query(Sale).filter_by(id=receipt.sale_id).first()
                        if sale:
                            sale.amount_paid -= receipt.amount_usd
                            sale.amount_due += receipt.amount_usd
                    # لا نحتاج لتحديث أي أرصدة للسندات بدون مرجع لأنها لا ترتبط بفواتير محددة

                    elif receipt.purchase_id:
                        purchase = session.query(Purchase).filter_by(id=receipt.purchase_id).first()
                        if purchase:
                            purchase.amount_paid -= receipt.amount_usd
                            purchase.amount_due += receipt.amount_usd                    # لا نحتاج لتحديث أي أرصدة للسندات بدون مرجع لأنها لا ترتبط بفواتير محددة
                    
                    # Delete journal entries first
                    session.query(JournalEntry).filter_by(receipt_id=receipt_id).delete()
                    
                    # Delete cash transaction records related to this receipt (if any)
                    # حذف جميع الحركات النقدية المرتبطة بهذا السند فقط دون عكس العملية
                    session.query(حركة_نقدية).filter(
                        (حركة_نقدية.reference.like(f"%سند {receipt_type} رقم {receipt_id}%")) |
                        (حركة_نقدية.reference.like(f"%سند قبض رقم {receipt_id}%")) |
                        (حركة_نقدية.reference.like(f"%سند صرف رقم {receipt_id}%"))
                    ).delete(synchronize_session=False)

                    # تحديث المخزون قبل حذف السند (إذا كان يحتوي على أصناف)
                    if receipt.has_items:
                        update_inventory_for_receipt(receipt_id, operation="remove")

                    # Delete receipt items first
                    session.query(ReceiptItem).filter_by(receipt_id=receipt_id).delete()

                    # Delete receipt
                    session.query(Receipt).filter_by(id=receipt_id).delete()

                    # Commit changes
                    session.commit()

                    # Show success message
                    QMessageBox.information(self, "نجاح", "تم حذف السند بنجاح")

                    # Reload vouchers data
                    self.load_vouchers_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف السند: {str(e)}")

    def print_voucher(self):
        """طباعة سند القبض أو الصرف المحدد"""
        import os
        from datetime import datetime
        import webbrowser

        # Get selected row
        selected_row = self.vouchers_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار سند للطباعة")
            return

        # Get voucher ID and type
        receipt_id = int(self.vouchers_table.item(selected_row, 0).text())
        receipt_type = self.vouchers_table.item(selected_row, 1).text()

        # إنشاء اسم ملف للسند
        now = datetime.now().strftime('%Y%m%d_%H%M%S')
        if receipt_type == "قبض":
            voucher_filename = f"receipt_voucher_{receipt_id}_{now}.html"
        else:
            voucher_filename = f"payment_voucher_{receipt_id}_{now}.html"

        try:
            # بيانات السند
            receipt_data = None
            company_name = "نظام إدارة مبيعات الألماس"
            company_logo = ""

            with session_scope() as session:
                # Get receipt
                receipt = session.query(Receipt).filter_by(id=receipt_id).first()

                if not receipt:
                    QMessageBox.warning(self, "تحذير", f"لم يتم العثور على السند رقم {receipt_id}")
                    return

                # تحضير بيانات السند
                current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                issue_date = receipt.issue_date.strftime("%Y-%m-%d") if receipt.issue_date else ""

                # البيانات المتعلقة بالمبيعات أو المشتريات
                related_entity = None
                entity_name = "غير محدد"
                entity_address = "-"
                entity_phone = "-"
                entity_id = "-"
                reference_info = "بدون مرجع"
                reference_details = {}

                # Get company info if available
                try:
                    from database import CompanyInfo
                    company_info = session.query(CompanyInfo).first()
                    if company_info:
                        company_name = company_info.name
                except:
                    # إذا لم يتم العثور على جدول معلومات الشركة، استخدام الاسم الافتراضي
                    pass

                # الحصول على بيانات إضافية حسب نوع السند
                if receipt.receipt_type == "CashIn":
                    if receipt.sale_id:  # سند قبض لمبيعات
                        sale_info = session.query(Sale, Customer).join(Customer).filter(Sale.id == receipt.sale_id).first()
                        if sale_info:
                            sale, customer = sale_info
                            entity_name = customer.name
                            entity_address = customer.address or "-"
                            entity_phone = customer.phone or "-"
                            entity_id = customer.id_number or "-"
                            reference_info = f"فاتورة مبيعات رقم {sale.id}"
                    elif receipt.customer_id:  # سند قبض بدون مرجع ولكن مرتبط بعميل
                        customer = session.query(Customer).filter_by(id=receipt.customer_id).first()
                        if customer:
                            entity_name = customer.name
                            entity_address = customer.address or "-"
                            entity_phone = customer.phone or "-"
                            entity_id = customer.id_number or "-"

                elif receipt.receipt_type == "CashOut":
                    if receipt.purchase_id:  # سند صرف لمشتريات
                        purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(Purchase.id == receipt.purchase_id).first()
                        if purchase_info:
                            purchase, supplier = purchase_info
                            entity_name = supplier.name
                            entity_address = supplier.address or "-"
                            entity_phone = supplier.phone or "-"
                            entity_id = supplier.id_number or "-"
                            reference_info = f"فاتورة مشتريات رقم {purchase.id}"
                    elif receipt.supplier_id:  # سند صرف بدون مرجع ولكن مرتبط بمورد
                        supplier = session.query(Supplier).filter_by(id=receipt.supplier_id).first()
                        if supplier:
                            entity_name = supplier.name
                            entity_address = supplier.address or "-"
                            entity_phone = supplier.phone or "-"
                            entity_id = supplier.id_number or "-"

                # تخزين بيانات السند
                receipt_data = {
                    "id": receipt.id,
                    "type": "سند قبض" if receipt.receipt_type == "CashIn" else "سند صرف",
                    "type_en": "Receipt Voucher" if receipt.receipt_type == "CashIn" else "Payment Voucher",
                    "amount_usd": receipt.amount_usd,
                    "amount_sar": receipt.amount_sar,
                    "exchange_rate": receipt.amount_sar / receipt.amount_usd if receipt.amount_usd > 0 else 0,
                    "issue_date": issue_date,
                    "entity_name": entity_name,
                    "entity_id": entity_id,
                    "entity_phone": entity_phone,
                    "entity_address": entity_address,
                    "reference_info": reference_info,
                    "current_date": current_date
                }

            # بناء محتوى ملف HTML للسند
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{receipt_data['type']} رقم {receipt_data['id']}</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background-color: #f9f9f9;
                        color: #333;
                    }}

                    .container {{
                        max-width: 800px;
                        margin: 0 auto;
                        background-color: #fff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                    }}

                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }}

                    .logo {{
                        max-width: 150px;
                        margin-bottom: 15px;
                    }}

                    .title {{
                        font-size: 28px;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }}

                    .subtitle {{
                        font-size: 20px;
                        font-weight: 500;
                        color: #3498db;
                        margin: 10px 0;
                    }}

                    .voucher-number {{
                        font-size: 18px;
                        color: #7f8c8d;
                    }}

                    .main-details {{
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 30px;
                        flex-wrap: wrap;
                    }}

                    .entity-details, .voucher-info {{
                        width: 48%;
                        background-color: #f8f9fa;
                        padding: 15px;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                    }}

                    @media (max-width: 768px) {{
                        .entity-details, .voucher-info {{
                            width: 100%;
                            margin-bottom: 15px;
                        }}
                    }}

                    .section-title {{
                        color: #3498db;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 5px;
                        margin-bottom: 15px;
                        font-size: 18px;
                    }}

                    .detail-row {{
                        display: flex;
                        margin-bottom: 8px;
                    }}

                    .detail-label {{
                        font-weight: 700;
                        width: 40%;
                        color: #7f8c8d;
                    }}

                    .detail-value {{
                        width: 60%;
                    }}

                    .amount-box {{
                        margin-top: 30px;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                        text-align: center;
                    }}

                    .amount-title {{
                        font-weight: 700;
                        margin-bottom: 10px;
                        font-size: 16px;
                        color: #7f8c8d;
                    }}

                    .amount-value {{
                        font-weight: 700;
                        font-size: 24px;
                        color: #2c3e50;
                    }}

                    .amount-in-words {{
                        margin-top: 10px;
                        font-size: 16px;
                        color: #34495e;
                        font-style: italic;
                    }}

                    .reference-box {{
                        margin-top: 30px;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                    }}

                    .reference-title {{
                        font-weight: 700;
                        margin-bottom: 10px;
                        font-size: 16px;
                        color: #3498db;
                    }}

                    .reference-value {{
                        font-weight: 500;
                        font-size: 16px;
                        color: #2c3e50;
                    }}

                    .signatures {{
                        display: flex;
                        justify-content: space-between;
                        margin-top: 50px;
                    }}

                    .signature {{
                        width: 200px;
                        text-align: center;
                        border-top: 1px solid #000;
                        padding-top: 5px;
                    }}

                    .footer {{
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #95a5a6;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }}

                    .print-button {{
                        background-color: #3498db;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        font-size: 16px;
                        border-radius: 5px;
                        cursor: pointer;
                        margin: 20px auto;
                        display: block;
                    }}

                    .print-button:hover {{
                        background-color: #2980b9;
                    }}

                    @media print {{
                        .print-button {{
                            display: none;
                        }}
                        body {{
                            background-color: white;
                        }}
                        .container {{
                            box-shadow: none;
                            padding: 0;
                        }}
                    }}
                </style>
                <script>
                    function printVoucher() {{
                        window.print();
                    }}
                </script>
            </head>
            <body>
                <div class="container">
                    <button onclick="printVoucher()" class="print-button">طباعة السند</button>

                    <div class="header">
                        {f'<img src="{company_logo}" alt="Company Logo" class="logo">' if company_logo else ''}
                        <div class="title">{company_name}</div>
                        <div class="subtitle">{receipt_data['type']}</div>
                        <div class="voucher-number">{receipt_data['type_en']} #{receipt_data['id']}</div>
                    </div>

                    <div class="main-details">
                        <div class="entity-details">
                            <div class="section-title">{'بيانات العميل' if receipt_data['type'] == 'سند قبض' else 'بيانات المورد'}</div>
                            <div class="detail-row">
                                <div class="detail-label">الاسم:</div>
                                <div class="detail-value">{receipt_data['entity_name']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">رقم الهوية:</div>
                                <div class="detail-value">{receipt_data['entity_id']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">رقم الهاتف:</div>
                                <div class="detail-value">{receipt_data['entity_phone']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">العنوان:</div>
                                <div class="detail-value">{receipt_data['entity_address']}</div>
                            </div>
                        </div>

                        <div class="voucher-info">
                            <div class="section-title">بيانات السند</div>
                            <div class="detail-row">
                                <div class="detail-label">رقم السند:</div>
                                <div class="detail-value">{receipt_data['id']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">تاريخ السند:</div>
                                <div class="detail-value">{receipt_data['issue_date']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">تاريخ الطباعة:</div>
                                <div class="detail-value">{receipt_data['current_date']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">نوع السند:</div>
                                <div class="detail-value">{receipt_data['type']}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">سعر الصرف:</div>
                                <div class="detail-value">{receipt_data['exchange_rate']:.3f} ريال/دولار</div>
                            </div>
                        </div>
                    </div>

                    <div class="amount-box">
                        <div class="amount-title">المبلغ بالدولار الأمريكي</div>
                        <div class="amount-value">${receipt_data['amount_usd']:.3f}</div>
                        <div class="amount-title" style="margin-top: 15px;">المبلغ بالريال السعودي</div>
                        <div class="amount-value">{receipt_data['amount_sar']:.3f} ريال</div>
                    </div>

                    <div class="reference-box">
                        <div class="reference-title">المرجع</div>
                        <div class="reference-value">{receipt_data['reference_info']}</div>
                    </div>

                    <div class="signatures">
                        <div class="signature">{'توقيع العميل' if receipt_data['type'] == 'سند قبض' else 'توقيع المورد'}</div>
                        <div class="signature">{'توقيع أمين الصندوق' if receipt_data['type'] == 'سند قبض' else 'توقيع المحاسب'}</div>
                        <div class="signature">توقيع المدير</div>
                    </div>

                    <div class="footer">
                        <p>شكراً للتعامل معنا</p>
                        <p>{company_name}</p>
                        <p>تم إنشاء هذا السند في {receipt_data['current_date']}</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # تسجيل العملية
            log_info(f"تم طباعة {receipt_data['type']} رقم {receipt_id}")

            # كتابة محتوى HTML إلى ملف
            with open(voucher_filename, 'w', encoding='utf-8') as html_file:
                html_file.write(html_content)

            # فتح ملف HTML في المتصفح الافتراضي
            file_path = os.path.abspath(voucher_filename)
            webbrowser.open('file://' + file_path, new=2)

            # عرض رسالة نجاح
            QMessageBox.information(self, "نجاح", f"تم إنشاء {receipt_data['type']} بنجاح وفتحه في المتصفح")

        except Exception as e:
            log_error(f"خطأ في طباعة السند: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة السند: {str(e)}")

    def edit_voucher(self):
        """تعديل السند المحدد"""
        # Get selected row
        selected_row = self.vouchers_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار سند لتعديله")
            return

        # Get receipt ID and type
        receipt_id = int(self.vouchers_table.item(selected_row, 0).text())
        receipt_type = self.vouchers_table.item(selected_row, 1).text()

        try:
            with session_scope() as session:
                # Get receipt
                receipt = session.query(Receipt).filter_by(id=receipt_id).first()

                if not receipt:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على السند")
                    return

                # Get related entity (customer or supplier)
                entity_name = "غير محدد"
                reference_info = None
                ref_id = None

                if receipt.receipt_type == "CashIn" and receipt.sale_id:
                    sale_info = session.query(Sale, Customer).join(Customer).filter(Sale.id == receipt.sale_id).first()
                    if sale_info:
                        sale, customer = sale_info
                        entity_name = customer.name
                        reference_info = f"فاتورة مبيعات رقم {sale.id}"
                        ref_id = sale.id
                        max_amount = sale.amount_due + receipt.amount_usd  # Current due + original receipt amount

                elif receipt.receipt_type == "CashOut" and receipt.purchase_id:
                    purchase_info = session.query(Purchase, Supplier).join(Supplier).filter(Purchase.id == receipt.purchase_id).first()
                    if purchase_info:
                        purchase, supplier = purchase_info
                        entity_name = supplier.name
                        reference_info = f"فاتورة مشتريات رقم {purchase.id}"
                        ref_id = purchase.id
                        max_amount = purchase.amount_due + receipt.amount_usd  # Current due + original receipt amount

                # Create edit dialog
                dialog = QDialog(self)
                dialog.setWindowTitle(f"تعديل سند {receipt_type} رقم {receipt_id}")
                dialog.setMinimumWidth(400)

                layout = QVBoxLayout(dialog)

                # Create title label
                title_label = QLabel(f"تعديل سند {receipt_type} رقم {receipt_id}")
                title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
                title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                layout.addWidget(title_label)

                # Create form for editing
                form_layout = QFormLayout()

                # Entity name (read-only)
                entity_label = QLabel(entity_name)
                form_layout.addRow(f"{'العميل' if receipt_type == 'قبض' else 'المورد'}:", entity_label)

                # Reference info (read-only)
                if reference_info:
                    ref_label = QLabel(reference_info)
                    form_layout.addRow("المرجع:", ref_label)
                else:
                    ref_label = QLabel("بدون مرجع")
                    form_layout.addRow("المرجع:", ref_label)

                # Currency selection
                currency_layout = QHBoxLayout()
                currency_group = QButtonGroup()

                usd_radio = QRadioButton("دولار أمريكي $")
                sar_radio = QRadioButton("ريال سعودي ﷼")
                usd_radio.setChecked(True)  # Default to USD

                currency_group.addButton(usd_radio, 1)
                currency_group.addButton(sar_radio, 2)

                currency_layout.addWidget(usd_radio)
                currency_layout.addWidget(sar_radio)

                currency_widget = QWidget()
                currency_widget.setLayout(currency_layout)
                form_layout.addRow("عملة السداد:", currency_widget)

                # Amount input USD
                amount_usd_spin = QDoubleSpinBox()
                amount_usd_spin.setRange(0.01, 1000000.0)
                amount_usd_spin.setDecimals(3)
                amount_usd_spin.setSingleStep(10.0)
                amount_usd_spin.setPrefix("$ ")
                amount_usd_spin.setValue(receipt.amount_usd)

                # If linked to sale/purchase, set maximum allowed amount
                if ref_id is not None and 'max_amount' in locals():
                    amount_usd_spin.setMaximum(max_amount)

                # Amount input SAR
                amount_sar_spin = QDoubleSpinBox()
                amount_sar_spin.setRange(0.01, 10000000.0)
                amount_sar_spin.setDecimals(3)
                amount_sar_spin.setSingleStep(50.0)
                amount_sar_spin.setPrefix("﷼ ")
                amount_sar_spin.setValue(receipt.amount_sar)
                amount_sar_spin.setVisible(False)  # Initially hidden

                # Exchange rate
                exchange_rate_spin = QDoubleSpinBox()
                exchange_rate_spin.setRange(0.01, 10.0)
                exchange_rate_spin.setDecimals(3)
                exchange_rate_spin.setSingleStep(0.01)
                exchange_rate_spin.setValue(receipt.amount_sar / receipt.amount_usd if receipt.amount_usd > 0 else 3.75)

                # SAR amount display
                amount_sar_label = QLabel(f"{receipt.amount_sar:.3f} ريال")

                # USD amount display
                amount_usd_label = QLabel(f"{receipt.amount_usd:.3f} دولار")
                amount_usd_label.setVisible(False)  # Initially hidden

                def update_sar_amount():
                    amount_sar = amount_usd_spin.value() * exchange_rate_spin.value()
                    amount_sar_label.setText(f"{amount_sar:.3f} ريال")

                def update_usd_amount():
                    exchange_rate = exchange_rate_spin.value()
                    if exchange_rate > 0:
                        amount_usd = amount_sar_spin.value() / exchange_rate
                        amount_usd_label.setText(f"{amount_usd:.3f} دولار")

                def toggle_currency():
                    is_usd = usd_radio.isChecked()
                    amount_usd_spin.setVisible(is_usd)
                    amount_sar_label.setVisible(is_usd)
                    amount_sar_spin.setVisible(not is_usd)
                    amount_usd_label.setVisible(not is_usd)

                    if is_usd:
                        update_sar_amount()
                    else:
                        update_usd_amount()

                amount_usd_spin.valueChanged.connect(update_sar_amount)
                amount_sar_spin.valueChanged.connect(update_usd_amount)
                exchange_rate_spin.valueChanged.connect(lambda: update_sar_amount() if usd_radio.isChecked() else update_usd_amount())
                usd_radio.toggled.connect(toggle_currency)
                sar_radio.toggled.connect(toggle_currency)

                # Date field
                issue_date = QDateEdit()
                issue_date.setDisplayFormat("dd/MM/yyyy")
                issue_date.setCalendarPopup(True)
                issue_date.setDate(QDate.fromString(receipt.issue_date.strftime("%Y-%m-%d"), "yyyy-MM-dd") if receipt.issue_date else QDate.currentDate())

                # Add fields to form
                form_layout.addRow("المبلغ بالدولار:", amount_usd_spin)
                form_layout.addRow("المبلغ بالريال:", amount_sar_spin)
                form_layout.addRow("المبلغ بالريال:", amount_sar_label)
                form_layout.addRow("المبلغ بالدولار:", amount_usd_label)
                form_layout.addRow("سعر الصرف (ريال/دولار):", exchange_rate_spin)
                form_layout.addRow("تاريخ السند:", issue_date)

                layout.addLayout(form_layout)                # Add buttons
                buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
                buttons.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
                buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
                buttons.accepted.connect(dialog.accept)
                buttons.rejected.connect(dialog.reject)
                style_dialog_buttons(buttons)
                layout.addWidget(buttons)

                # Show dialog
                if dialog.exec() == QDialog.Accepted:
                    # Get form data based on selected currency
                    is_usd_mode = usd_radio.isChecked()

                    if is_usd_mode:
                        # إذا تم الإدخال بالدولار
                        new_amount_usd = amount_usd_spin.value()
                        new_exchange_rate = exchange_rate_spin.value()
                        new_amount_sar = new_amount_usd * new_exchange_rate
                    else:
                        # إذا تم الإدخال بالريال
                        new_amount_sar = amount_sar_spin.value()
                        new_exchange_rate = exchange_rate_spin.value()
                        new_amount_usd = new_amount_sar / new_exchange_rate if new_exchange_rate > 0 else 0

                    new_issue_date = issue_date.date().toPyDate()

                    # Calculate difference for updating related entities
                    amount_difference = new_amount_usd - receipt.amount_usd

                    # Update receipt
                    receipt.amount_usd = new_amount_usd
                    receipt.amount_sar = new_amount_sar
                    receipt.issue_date = new_issue_date

                    # Update related entities if needed
                    if receipt.sale_id and amount_difference != 0:
                        sale = session.query(Sale).filter_by(id=receipt.sale_id).first()
                        if sale:
                            sale.amount_paid += amount_difference
                            sale.amount_due -= amount_difference

                    elif receipt.purchase_id and amount_difference != 0:
                        purchase = session.query(Purchase).filter_by(id=receipt.purchase_id).first()
                        if purchase:
                            purchase.amount_paid += amount_difference
                            purchase.amount_due -= amount_difference

                    # Update related journal entries
                    journal_entries = session.query(JournalEntry).filter_by(receipt_id=receipt_id).all()
                    for entry in journal_entries:
                        if entry.debit > 0:
                            entry.debit = new_amount_usd
                        elif entry.credit > 0:
                            entry.credit = new_amount_usd
                        entry.date = new_issue_date

                    # Commit changes
                    session.commit()

                    # تسجيل العملية
                    log_info(f"تم تعديل سند {receipt_type} رقم {receipt_id} بقيمة {new_amount_usd:.3f}$ ({new_amount_sar:.3f} ريال)")

                    # Show success message
                    QMessageBox.information(self, "نجاح", f"تم تعديل سند ال{receipt_type} بنجاح")

                    # Reload vouchers data
                    self.load_vouchers_data()

        except Exception as e:
            log_error(f"خطأ في تعديل السند: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل السند: {str(e)}")

    def refresh_vouchers(self):
        """دالة لتحديث قائمة السندات يدويًا"""
        try:
            log_info("بدء تحديث قائمة السندات")
            self.vouchers_table.clearContents()
            self.vouchers_table.setRowCount(0)
            self.load_vouchers_data()
            QMessageBox.information(self, "تم التحديث", "تم تحديث قائمة السندات بنجاح")
        except Exception as e:
            log_error(f"خطأ في تحديث قائمة السندات: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث قائمة السندات: {str(e)}")


class ReceiptItemDialog(QDialog):
    """حوار إضافة/تعديل صنف في السند"""

    def __init__(self, parent=None, item_data=None):
        super().__init__(parent)
        self.item_data = item_data
        self.exchange_rate = 3.75  # سعر الصرف الافتراضي
        self.init_ui()
        self.load_data()

        if item_data:
            self.populate_fields()

    def init_ui(self):
        self.setWindowTitle("إضافة صنف" if not self.item_data else "تعديل صنف")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # اختيار الصنف
        self.category_combo = QComboBox()
        self.category_combo.currentIndexChanged.connect(self.load_units)
        form_layout.addRow("الصنف:", self.category_combo)

        # اختيار الوحدة
        self.unit_combo = QComboBox()
        form_layout.addRow("الوحدة:", self.unit_combo)

        # نوع الألماس
        self.diamond_type_edit = QLineEdit()
        form_layout.addRow("نوع الألماس:", self.diamond_type_edit)

        # الكمية
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.001, 999999.999)
        self.quantity_spin.setDecimals(3)
        self.quantity_spin.setSingleStep(0.1)
        self.quantity_spin.valueChanged.connect(self.calculate_totals)
        form_layout.addRow("الكمية:", self.quantity_spin)

        # سعر الوحدة بالدولار
        self.price_usd_spin = QDoubleSpinBox()
        self.price_usd_spin.setRange(0.01, 999999.99)
        self.price_usd_spin.setDecimals(3)
        self.price_usd_spin.setSingleStep(1.0)
        self.price_usd_spin.setPrefix("$ ")
        self.price_usd_spin.valueChanged.connect(self.calculate_totals)
        form_layout.addRow("سعر الوحدة ($):", self.price_usd_spin)

        # سعر الوحدة بالريال (للعرض فقط)
        self.price_sar_label = QLabel("0.000 ريال")
        form_layout.addRow("سعر الوحدة (ريال):", self.price_sar_label)

        # الإجمالي بالدولار (للعرض فقط)
        self.total_usd_label = QLabel("$0.000")
        form_layout.addRow("الإجمالي ($):", self.total_usd_label)

        # الإجمالي بالريال (للعرض فقط)
        self.total_sar_label = QLabel("0.000 ريال")
        form_layout.addRow("الإجمالي (ريال):", self.total_sar_label)

        # سعر الصرف
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.01, 10.0)
        self.exchange_rate_spin.setDecimals(3)
        self.exchange_rate_spin.setValue(3.75)
        self.exchange_rate_spin.valueChanged.connect(self.update_exchange_rate)
        form_layout.addRow("سعر الصرف:", self.exchange_rate_spin)

        layout.addLayout(form_layout)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        style_dialog_buttons(buttons)
        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            with session_scope() as session:
                # تحميل الأصناف
                categories = session.query(Category).all()
                for category in categories:
                    self.category_combo.addItem(category.name, category.id)

                # تحميل الوحدات للصنف الأول
                if categories:
                    self.load_units()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def load_units(self):
        """تحميل الوحدات للصنف المحدد"""
        self.unit_combo.clear()
        category_id = self.category_combo.currentData()

        if category_id:
            try:
                with session_scope() as session:
                    units = session.query(Unit).filter_by(category_id=category_id).all()
                    for unit in units:
                        self.unit_combo.addItem(unit.name, unit.id)

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الوحدات: {str(e)}")

    def update_exchange_rate(self):
        """تحديث سعر الصرف وإعادة حساب القيم"""
        self.exchange_rate = self.exchange_rate_spin.value()
        self.calculate_totals()

    def calculate_totals(self):
        """حساب الإجماليات"""
        quantity = self.quantity_spin.value()
        price_usd = self.price_usd_spin.value()

        # حساب سعر الوحدة بالريال
        price_sar = price_usd * self.exchange_rate
        self.price_sar_label.setText(f"{price_sar:.3f} ريال")

        # حساب الإجماليات
        total_usd = quantity * price_usd
        total_sar = total_usd * self.exchange_rate

        self.total_usd_label.setText(f"${total_usd:.3f}")
        self.total_sar_label.setText(f"{total_sar:.3f} ريال")

    def populate_fields(self):
        """ملء الحقول بالبيانات الموجودة (للتعديل)"""
        if not self.item_data:
            return

        # تحديد الصنف
        for i in range(self.category_combo.count()):
            if self.category_combo.itemData(i) == self.item_data['category_id']:
                self.category_combo.setCurrentIndex(i)
                break

        # تحديد الوحدة
        for i in range(self.unit_combo.count()):
            if self.unit_combo.itemData(i) == self.item_data['unit_id']:
                self.unit_combo.setCurrentIndex(i)
                break

        # ملء باقي الحقول
        self.diamond_type_edit.setText(self.item_data['diamond_type'])
        self.quantity_spin.setValue(self.item_data['quantity'])
        self.price_usd_spin.setValue(self.item_data['price_per_unit_usd'])
        self.exchange_rate_spin.setValue(self.item_data['exchange_rate'])

    def get_item_data(self):
        """الحصول على بيانات الصنف"""
        category_id = self.category_combo.currentData()
        unit_id = self.unit_combo.currentData()

        if not category_id or not unit_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الصنف والوحدة")
            return None

        if not self.diamond_type_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الألماس")
            return None

        quantity = self.quantity_spin.value()
        price_usd = self.price_usd_spin.value()

        if quantity <= 0 or price_usd <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية وسعر صحيحين")
            return None

        price_sar = price_usd * self.exchange_rate
        total_usd = quantity * price_usd
        total_sar = total_usd * self.exchange_rate

        return {
            'category_id': category_id,
            'category_name': self.category_combo.currentText(),
            'unit_id': unit_id,
            'unit_name': self.unit_combo.currentText(),
            'diamond_type': self.diamond_type_edit.text().strip(),
            'quantity': quantity,
            'price_per_unit_usd': price_usd,
            'price_per_unit_sar': price_sar,
            'total_price_usd': total_usd,
            'total_price_sar': total_sar,
            'exchange_rate': self.exchange_rate
        }

    def accept(self):
        """التحقق من صحة البيانات قبل القبول"""
        if self.get_item_data():
            super().accept()