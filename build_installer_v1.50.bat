@echo off
chcp 65001 >nul
echo ============================================================
echo 🏗️  بناء مثبت Diamond Sales v1.50
echo ============================================================
echo.

REM التحقق من وجود Inno Setup
set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if not exist "%INNO_PATH%" (
    echo ❌ Inno Setup غير مثبت في المسار الافتراضي
    echo 💡 يرجى تثبيت Inno Setup من: https://jrsoftware.org/isinfo.php
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود ملف المثبت
if not exist "DiamondSales_installer.iss" (
    echo ❌ ملف DiamondSales_installer.iss غير موجود
    echo 💡 يرجى تشغيل build_exe.py أولاً
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود مجلد التوزيع
if not exist "dist\DiamondSales\DiamondSales.exe" (
    echo ❌ الملف التنفيذي غير موجود
    echo 💡 يرجى تشغيل build_exe.py أولاً
    echo.
    pause
    exit /b 1
)

REM إنشاء مجلد الإخراج
if not exist "installer_output" mkdir installer_output

echo 🔨 بناء المثبت...
echo.

REM تشغيل Inno Setup
"%INNO_PATH%" "DiamondSales_installer.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء المثبت بنجاح!
    echo 📁 الملف موجود في: installer_output\DiamondSalesSetup_1.50.exe
    echo.
    
    REM فتح مجلد الإخراج
    if exist "installer_output\DiamondSalesSetup_1.50.exe" (
        echo 🎉 فتح مجلد الإخراج...
        explorer installer_output
    )
) else (
    echo.
    echo ❌ فشل في بناء المثبت
    echo 💡 يرجى التحقق من رسائل الخطأ أعلاه
)

echo.
pause
