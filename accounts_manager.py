#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة حسابات العملاء والموردين
تحتوي على وظائف لحساب أرصدة العملاء والموردين مع مراعاة السندات بالأصناف
"""

from database import Receipt, ReceiptItem, Customer, Supplier, Sale, Purchase
from db_session import session_scope
from logger import log_info, log_error
from sqlalchemy import func, and_, or_
from datetime import datetime


def calculate_customer_balance(customer_id, include_items_value=True, as_of_date=None):
    """
    حساب رصيد العميل مع مراعاة السندات بالأصناف
    
    Args:
        customer_id (int): معرف العميل
        include_items_value (bool): هل يتم تضمين قيمة الأصناف في السندات
        as_of_date (datetime, optional): حساب الرصيد حتى تاريخ معين
    
    Returns:
        dict: تفاصيل رصيد العميل
    """
    try:
        with session_scope() as session:
            customer = session.query(Customer).filter_by(id=customer_id).first()
            if not customer:
                return None
            
            # الرصيد الافتتاحي
            opening_balance = customer.opening_balance or 0
            
            # إجمالي المبيعات
            sales_query = session.query(func.coalesce(func.sum(Sale.total_price_usd), 0)).filter(
                Sale.customer_id == customer_id
            )
            if as_of_date:
                sales_query = sales_query.filter(Sale.sale_date <= as_of_date)
            
            total_sales = sales_query.scalar() or 0
            
            # إجمالي المدفوعات النقدية من سندات القبض
            cash_receipts_query = session.query(func.coalesce(func.sum(Receipt.amount_usd), 0)).filter(
                and_(
                    Receipt.customer_id == customer_id,
                    Receipt.receipt_type == "CashIn",
                    Receipt.has_items == False  # السندات النقدية فقط
                )
            )
            if as_of_date:
                cash_receipts_query = cash_receipts_query.filter(Receipt.issue_date <= as_of_date)
            
            cash_receipts = cash_receipts_query.scalar() or 0
            
            # قيمة الأصناف في سندات القبض (إذا كان مطلوباً)
            items_receipts_value = 0
            if include_items_value:
                items_receipts_query = session.query(func.coalesce(func.sum(Receipt.amount_usd), 0)).filter(
                    and_(
                        Receipt.customer_id == customer_id,
                        Receipt.receipt_type == "CashIn",
                        Receipt.has_items == True  # السندات بالأصناف
                    )
                )
                if as_of_date:
                    items_receipts_query = items_receipts_query.filter(Receipt.issue_date <= as_of_date)
                
                items_receipts_value = items_receipts_query.scalar() or 0
            
            # حساب الرصيد النهائي
            # الرصيد = الرصيد الافتتاحي + إجمالي المبيعات - المدفوعات النقدية - قيمة الأصناف المستلمة
            final_balance = opening_balance + total_sales - cash_receipts - items_receipts_value
            
            return {
                'customer_id': customer_id,
                'customer_name': customer.name,
                'opening_balance': opening_balance,
                'total_sales': total_sales,
                'cash_receipts': cash_receipts,
                'items_receipts_value': items_receipts_value,
                'total_receipts': cash_receipts + items_receipts_value,
                'final_balance': final_balance
            }
            
    except Exception as e:
        log_error(f"خطأ في حساب رصيد العميل {customer_id}: {str(e)}", e)
        return None


def calculate_supplier_balance(supplier_id, include_items_value=True, as_of_date=None):
    """
    حساب رصيد المورد مع مراعاة السندات بالأصناف
    
    Args:
        supplier_id (int): معرف المورد
        include_items_value (bool): هل يتم تضمين قيمة الأصناف في السندات
        as_of_date (datetime, optional): حساب الرصيد حتى تاريخ معين
    
    Returns:
        dict: تفاصيل رصيد المورد
    """
    try:
        with session_scope() as session:
            supplier = session.query(Supplier).filter_by(id=supplier_id).first()
            if not supplier:
                return None
            
            # الرصيد الافتتاحي
            opening_balance = supplier.opening_balance or 0
            
            # إجمالي المشتريات
            purchases_query = session.query(func.coalesce(func.sum(Purchase.total_price_usd), 0)).filter(
                Purchase.supplier_id == supplier_id
            )
            if as_of_date:
                purchases_query = purchases_query.filter(Purchase.purchase_date <= as_of_date)
            
            total_purchases = purchases_query.scalar() or 0
            
            # إجمالي المدفوعات النقدية من سندات الصرف
            cash_payments_query = session.query(func.coalesce(func.sum(Receipt.amount_usd), 0)).filter(
                and_(
                    Receipt.supplier_id == supplier_id,
                    Receipt.receipt_type == "CashOut",
                    Receipt.has_items == False  # السندات النقدية فقط
                )
            )
            if as_of_date:
                cash_payments_query = cash_payments_query.filter(Receipt.issue_date <= as_of_date)
            
            cash_payments = cash_payments_query.scalar() or 0
            
            # قيمة الأصناف في سندات الصرف (إذا كان مطلوباً)
            items_payments_value = 0
            if include_items_value:
                items_payments_query = session.query(func.coalesce(func.sum(Receipt.amount_usd), 0)).filter(
                    and_(
                        Receipt.supplier_id == supplier_id,
                        Receipt.receipt_type == "CashOut",
                        Receipt.has_items == True  # السندات بالأصناف
                    )
                )
                if as_of_date:
                    items_payments_query = items_payments_query.filter(Receipt.issue_date <= as_of_date)
                
                items_payments_value = items_payments_query.scalar() or 0
            
            # حساب الرصيد النهائي
            # الرصيد = الرصيد الافتتاحي + إجمالي المشتريات - المدفوعات النقدية - قيمة الأصناف المدفوعة
            final_balance = opening_balance + total_purchases - cash_payments - items_payments_value
            
            return {
                'supplier_id': supplier_id,
                'supplier_name': supplier.name,
                'opening_balance': opening_balance,
                'total_purchases': total_purchases,
                'cash_payments': cash_payments,
                'items_payments_value': items_payments_value,
                'total_payments': cash_payments + items_payments_value,
                'final_balance': final_balance
            }
            
    except Exception as e:
        log_error(f"خطأ في حساب رصيد المورد {supplier_id}: {str(e)}", e)
        return None


def get_customers_balances_report(include_items_value=True, as_of_date=None):
    """
    تقرير أرصدة جميع العملاء
    
    Args:
        include_items_value (bool): هل يتم تضمين قيمة الأصناف في السندات
        as_of_date (datetime, optional): حساب الأرصدة حتى تاريخ معين
    
    Returns:
        list: قائمة بأرصدة العملاء
    """
    try:
        with session_scope() as session:
            customers = session.query(Customer).all()
            
            balances_report = []
            for customer in customers:
                balance_data = calculate_customer_balance(
                    customer.id, 
                    include_items_value=include_items_value,
                    as_of_date=as_of_date
                )
                if balance_data:
                    balances_report.append(balance_data)
            
            # ترتيب حسب الرصيد النهائي (الأعلى أولاً)
            balances_report.sort(key=lambda x: x['final_balance'], reverse=True)
            
            return balances_report
            
    except Exception as e:
        log_error(f"خطأ في إنشاء تقرير أرصدة العملاء: {str(e)}", e)
        return []


def get_suppliers_balances_report(include_items_value=True, as_of_date=None):
    """
    تقرير أرصدة جميع الموردين
    
    Args:
        include_items_value (bool): هل يتم تضمين قيمة الأصناف في السندات
        as_of_date (datetime, optional): حساب الأرصدة حتى تاريخ معين
    
    Returns:
        list: قائمة بأرصدة الموردين
    """
    try:
        with session_scope() as session:
            suppliers = session.query(Supplier).all()
            
            balances_report = []
            for supplier in suppliers:
                balance_data = calculate_supplier_balance(
                    supplier.id, 
                    include_items_value=include_items_value,
                    as_of_date=as_of_date
                )
                if balance_data:
                    balances_report.append(balance_data)
            
            # ترتيب حسب الرصيد النهائي (الأعلى أولاً)
            balances_report.sort(key=lambda x: x['final_balance'], reverse=True)
            
            return balances_report
            
    except Exception as e:
        log_error(f"خطأ في إنشاء تقرير أرصدة الموردين: {str(e)}", e)
        return []


def get_receipt_items_summary(receipt_id):
    """
    الحصول على ملخص أصناف السند
    
    Args:
        receipt_id (int): معرف السند
    
    Returns:
        dict: ملخص أصناف السند
    """
    try:
        with session_scope() as session:
            receipt = session.query(Receipt).filter_by(id=receipt_id).first()
            if not receipt or not receipt.has_items:
                return None
            
            items = session.query(ReceiptItem).filter_by(receipt_id=receipt_id).all()
            if not items:
                return None
            
            items_summary = []
            total_quantity = 0
            total_value_usd = 0
            total_value_sar = 0
            
            for item in items:
                # الحصول على أسماء الصنف والوحدة
                category = session.query(Category).filter_by(id=item.category_id).first()
                unit = session.query(Unit).filter_by(id=item.unit_id).first()
                
                item_data = {
                    'diamond_type': item.diamond_type,
                    'category_name': category.name if category else "غير محدد",
                    'unit_name': unit.name if unit else "غير محدد",
                    'quantity': item.quantity,
                    'price_per_unit_usd': item.price_per_unit_usd,
                    'price_per_unit_sar': item.price_per_unit_sar,
                    'total_price_usd': item.total_price_usd,
                    'total_price_sar': item.total_price_sar
                }
                items_summary.append(item_data)
                
                total_quantity += item.quantity
                total_value_usd += item.total_price_usd
                total_value_sar += item.total_price_sar
            
            return {
                'receipt_id': receipt_id,
                'receipt_type': receipt.receipt_type,
                'items': items_summary,
                'total_quantity': total_quantity,
                'total_value_usd': total_value_usd,
                'total_value_sar': total_value_sar,
                'items_count': len(items_summary)
            }
            
    except Exception as e:
        log_error(f"خطأ في الحصول على ملخص أصناف السند {receipt_id}: {str(e)}", e)
        return None
