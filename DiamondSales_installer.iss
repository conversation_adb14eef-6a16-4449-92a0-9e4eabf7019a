[Setup]
AppName=Diamond Sales
AppVersion=1.50
AppPublisher=Diamond Sales Company
AppPublisherURL=https://diamondsales.com
AppSupportURL=https://diamondsales.com/support
AppUpdatesURL=https://diamondsales.com/updates
DefaultDirName={autopf}\Diamond Sales
DefaultGroupName=Diamond Sales
AllowNoIcons=yes
LicenseFile=
OutputDir=installer_output
OutputBaseFilename=DiamondSalesSetup_1.50
SetupIconFile=assets\diamond_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "dist\DiamondSales\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\Diamond Sales"; Filename: "{app}\DiamondSales.exe"
Name: "{group}\{cm:UninstallProgram,Diamond Sales}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Diamond Sales"; Filename: "{app}\DiamondSales.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\DiamondSales.exe"; Description: "{cm:LaunchProgram,Diamond Sales}"; Flags: nowait postinstall skipifsilent
