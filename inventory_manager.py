#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المخزون
تحتوي على وظائف لتحديث كميات المخزون عند إضافة أو حذف سندات تحتوي على أصناف
"""

from database import Receipt, ReceiptItem, OpeningBalance, Category, Unit, Sale, Purchase
from db_session import session_scope
from logger import log_info, log_error
from datetime import datetime
from sqlalchemy import func


def update_inventory_for_receipt(receipt_id, operation="add"):
    """
    تحديث المخزون عند إضافة أو حذف سند يحتوي على أصناف
    
    Args:
        receipt_id (int): معرف السند
        operation (str): نوع العملية ("add" للإضافة، "remove" للحذف)
    
    Returns:
        bool: True إذا تم التحديث بنجاح، False خلاف ذلك
    """
    try:
        with session_scope() as session:
            # الحصول على السند وتفاصيله
            receipt = session.query(Receipt).filter_by(id=receipt_id).first()
            if not receipt or not receipt.has_items:
                return True  # لا يوجد أصناف للتحديث
            
            receipt_items = session.query(ReceiptItem).filter_by(receipt_id=receipt_id).all()
            if not receipt_items:
                return True
            
            for item in receipt_items:
                # تحديد اتجاه التأثير على المخزون
                if receipt.receipt_type == "CashIn":
                    # سند قبض: زيادة في المخزون (العميل يرد أصناف)
                    quantity_change = item.quantity if operation == "add" else -item.quantity
                elif receipt.receipt_type == "CashOut":
                    # سند صرف: نقص في المخزون (صرف أصناف للمورد)
                    quantity_change = -item.quantity if operation == "add" else item.quantity
                else:
                    continue
                
                # تحديث أو إنشاء رصيد افتتاحي للصنف
                update_opening_balance(
                    session=session,
                    diamond_type=item.diamond_type,
                    category_id=item.category_id,
                    unit_id=item.unit_id,
                    quantity_change=quantity_change,
                    price_per_unit_usd=item.price_per_unit_usd,
                    exchange_rate=item.exchange_rate
                )
            
            log_info(f"تم تحديث المخزون للسند رقم {receipt_id} - العملية: {operation}")
            return True
            
    except Exception as e:
        log_error(f"خطأ في تحديث المخزون للسند {receipt_id}: {str(e)}", e)
        return False


def update_opening_balance(session, diamond_type, category_id, unit_id, quantity_change, 
                          price_per_unit_usd, exchange_rate):
    """
    تحديث الرصيد الافتتاحي لصنف معين
    
    Args:
        session: جلسة قاعدة البيانات
        diamond_type (str): نوع الألماس
        category_id (int): معرف الصنف
        unit_id (int): معرف الوحدة
        quantity_change (float): التغيير في الكمية
        price_per_unit_usd (float): سعر الوحدة بالدولار
        exchange_rate (float): سعر الصرف
    """
    try:
        # البحث عن رصيد افتتاحي موجود
        opening_balance = session.query(OpeningBalance).filter_by(
            diamond_type=diamond_type,
            category_id=category_id,
            unit_id=unit_id
        ).first()
        
        if opening_balance:
            # تحديث الرصيد الموجود
            old_quantity = opening_balance.quantity
            new_quantity = old_quantity + quantity_change
            
            if new_quantity <= 0:
                # إذا أصبحت الكمية صفر أو أقل، احذف الرصيد
                session.delete(opening_balance)
                log_info(f"تم حذف الرصيد الافتتاحي للصنف {diamond_type} - الكمية أصبحت {new_quantity}")
            else:
                # تحديث الكمية والقيم
                opening_balance.quantity = new_quantity
                
                # حساب متوسط السعر المرجح
                old_total_value = old_quantity * opening_balance.price_per_carat_usd
                new_value = quantity_change * price_per_unit_usd
                total_value = old_total_value + new_value
                
                if new_quantity > 0:
                    opening_balance.price_per_carat_usd = total_value / new_quantity
                    opening_balance.total_value_usd = total_value
                    opening_balance.total_value_sar = total_value * exchange_rate
                    opening_balance.exchange_rate = exchange_rate
                
                log_info(f"تم تحديث الرصيد الافتتاحي للصنف {diamond_type} - الكمية الجديدة: {new_quantity}")
        
        elif quantity_change > 0:
            # إنشاء رصيد افتتاحي جديد (فقط إذا كان التغيير موجب)
            total_value_usd = quantity_change * price_per_unit_usd
            total_value_sar = total_value_usd * exchange_rate
            
            new_opening_balance = OpeningBalance(
                diamond_type=diamond_type,
                quantity=quantity_change,
                price_per_carat_usd=price_per_unit_usd,
                total_value_usd=total_value_usd,
                total_value_sar=total_value_sar,
                exchange_rate=exchange_rate,
                category_id=category_id,
                unit_id=unit_id,
                date_created=datetime.now(),
                notes=f"تم إنشاؤه تلقائياً من سند"
            )
            session.add(new_opening_balance)
            log_info(f"تم إنشاء رصيد افتتاحي جديد للصنف {diamond_type} - الكمية: {quantity_change}")
            
    except Exception as e:
        log_error(f"خطأ في تحديث الرصيد الافتتاحي للصنف {diamond_type}: {str(e)}", e)
        raise


def get_inventory_balance(diamond_type=None, category_id=None, unit_id=None):
    """
    الحصول على رصيد المخزون لصنف معين أو جميع الأصناف
    
    Args:
        diamond_type (str, optional): نوع الألماس
        category_id (int, optional): معرف الصنف
        unit_id (int, optional): معرف الوحدة
    
    Returns:
        list: قائمة بأرصدة المخزون
    """
    try:
        with session_scope() as session:
            query = session.query(
                OpeningBalance.diamond_type,
                OpeningBalance.quantity,
                OpeningBalance.price_per_carat_usd,
                OpeningBalance.total_value_usd,
                OpeningBalance.total_value_sar,
                Category.name.label('category_name'),
                Unit.name.label('unit_name'),
                Unit.symbol.label('unit_symbol')
            ).join(Category, OpeningBalance.category_id == Category.id)\
             .join(Unit, OpeningBalance.unit_id == Unit.id)
            
            # تطبيق المرشحات
            if diamond_type:
                query = query.filter(OpeningBalance.diamond_type == diamond_type)
            if category_id:
                query = query.filter(OpeningBalance.category_id == category_id)
            if unit_id:
                query = query.filter(OpeningBalance.unit_id == unit_id)
            
            results = query.all()
            
            inventory_data = []
            for result in results:
                inventory_data.append({
                    'diamond_type': result.diamond_type,
                    'quantity': result.quantity,
                    'price_per_unit_usd': result.price_per_carat_usd,
                    'total_value_usd': result.total_value_usd,
                    'total_value_sar': result.total_value_sar,
                    'category_name': result.category_name,
                    'unit_name': result.unit_name,
                    'unit_symbol': result.unit_symbol
                })
            
            return inventory_data
            
    except Exception as e:
        log_error(f"خطأ في الحصول على رصيد المخزون: {str(e)}", e)
        return []


def calculate_inventory_movement(start_date=None, end_date=None):
    """
    حساب حركة المخزون خلال فترة معينة
    
    Args:
        start_date (datetime, optional): تاريخ البداية
        end_date (datetime, optional): تاريخ النهاية
    
    Returns:
        dict: تقرير حركة المخزون
    """
    try:
        with session_scope() as session:
            # إعداد الاستعلام الأساسي
            receipt_items_query = session.query(ReceiptItem).join(Receipt)
            
            if start_date:
                receipt_items_query = receipt_items_query.filter(Receipt.issue_date >= start_date)
            if end_date:
                receipt_items_query = receipt_items_query.filter(Receipt.issue_date <= end_date)
            
            receipt_items = receipt_items_query.all()
            
            movement_data = {}
            
            for item in receipt_items:
                key = f"{item.diamond_type}_{item.category_id}_{item.unit_id}"
                
                if key not in movement_data:
                    movement_data[key] = {
                        'diamond_type': item.diamond_type,
                        'category_id': item.category_id,
                        'unit_id': item.unit_id,
                        'receipts_in': 0,  # سندات القبض (زيادة)
                        'receipts_out': 0,  # سندات الصرف (نقص)
                        'net_change': 0
                    }
                
                # تحديد نوع الحركة
                receipt = session.query(Receipt).filter_by(id=item.receipt_id).first()
                if receipt:
                    if receipt.receipt_type == "CashIn":
                        movement_data[key]['receipts_in'] += item.quantity
                    elif receipt.receipt_type == "CashOut":
                        movement_data[key]['receipts_out'] += item.quantity
            
            # حساب صافي التغيير
            for key in movement_data:
                data = movement_data[key]
                data['net_change'] = data['receipts_in'] - data['receipts_out']
            
            return movement_data
            
    except Exception as e:
        log_error(f"خطأ في حساب حركة المخزون: {str(e)}", e)
        return {}


def validate_inventory_availability(diamond_type, category_id, unit_id, required_quantity):
    """
    التحقق من توفر كمية كافية في المخزون
    
    Args:
        diamond_type (str): نوع الألماس
        category_id (int): معرف الصنف
        unit_id (int): معرف الوحدة
        required_quantity (float): الكمية المطلوبة
    
    Returns:
        tuple: (bool, float) - (متوفر أم لا، الكمية المتاحة)
    """
    try:
        with session_scope() as session:
            opening_balance = session.query(OpeningBalance).filter_by(
                diamond_type=diamond_type,
                category_id=category_id,
                unit_id=unit_id
            ).first()
            
            available_quantity = opening_balance.quantity if opening_balance else 0
            is_available = available_quantity >= required_quantity
            
            return is_available, available_quantity
            
    except Exception as e:
        log_error(f"خطأ في التحقق من توفر المخزون: {str(e)}", e)
        return False, 0
